#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Bybit API的不同端点
"""

import requests
import time

def test_bybit_endpoints():
    """测试不同的Bybit API端点"""
    
    # 测试symbol
    test_symbol = "BTCUSDT"
    
    # 可能的API端点
    base_url = "https://api.bybit.com"
    endpoints_and_params = [
        # V5 API
        ("/v5/market/tickers", {"category": "linear", "symbol": test_symbol}),
        ("/v5/market/tickers", {"category": "linear"}),  # 获取所有
        ("/v5/market/kline", {"category": "linear", "symbol": test_symbol, "interval": "1", "limit": 1}),
        
        # V2 API
        ("/v2/public/tickers", {"symbol": test_symbol}),
        ("/public/linear/recent-trading-records", {"symbol": test_symbol}),
        
        # 其他可能的端点
        ("/derivatives/v3/public/tickers", {"symbol": test_symbol}),
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    print(f"测试Bybit API端点 - {test_symbol}")
    print("=" * 60)
    
    for endpoint, params in endpoints_and_params:
        try:
            url = f"{base_url}{endpoint}"
            
            print(f"\n尝试端点: {endpoint}")
            print(f"参数: {params}")
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                
                if data.get("retCode") == 0:
                    result = data.get("result", {})
                    
                    # V5 API - tickers
                    if "list" in result:
                        ticker_list = result["list"]
                        print(f"✓ 获取到 {len(ticker_list)} 个ticker")
                        
                        if ticker_list:
                            first_ticker = ticker_list[0]
                            if "turnover24h" in first_ticker:
                                turnover = first_ticker["turnover24h"]
                                symbol_name = first_ticker.get("symbol", "Unknown")
                                print(f"  示例: {symbol_name} 24h成交额: {turnover}")
                            
                            # 如果是获取所有symbol，查找目标symbol
                            if len(ticker_list) > 1:
                                target = None
                                for ticker in ticker_list:
                                    if ticker.get("symbol") == test_symbol:
                                        target = ticker
                                        break
                                if target:
                                    print(f"  找到目标 {test_symbol}: 24h成交额 {target.get('turnover24h', 'N/A')}")
                                else:
                                    print(f"  未找到目标 {test_symbol}")
                    
                    # V2 API
                    elif "turnover_24h" in result:
                        print(f"✓ V2 API - 24h成交额: {result['turnover_24h']}")
                    
                    # K线数据
                    elif "list" in result and endpoint.endswith("kline"):
                        kline_list = result["list"]
                        if kline_list:
                            print(f"✓ K线数据 - 最新价格: {kline_list[0][4]}")  # 收盘价
                    
                    else:
                        print(f"✓ 其他数据结构: {list(result.keys()) if isinstance(result, dict) else result}")
                        
                elif data.get("retCode") != 0:
                    print(f"✗ API错误: {data.get('retMsg', 'Unknown error')}")
                else:
                    print(f"✗ 未知响应格式: {data}")
                    
            elif response.status_code == 403:
                print("✗ 403 Forbidden - API访问被拒绝")
            elif response.status_code == 404:
                print("✗ 404 Not Found - 端点不存在")
            else:
                print(f"✗ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"✗ 异常: {e}")
            
        time.sleep(0.5)  # 避免请求过于频繁

def test_public_endpoints():
    """测试公开端点"""
    print("\n\n测试Bybit公开端点...")
    print("=" * 40)
    
    try:
        # 测试服务器时间
        url = "https://api.bybit.com/v5/market/time"
        response = requests.get(url, timeout=10)
        
        print(f"服务器时间API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get("retCode") == 0:
                server_time = data.get("result", {}).get("timeSecond", "Unknown")
                print(f"✓ 服务器连接正常，时间: {server_time}")
            else:
                print(f"✗ 服务器时间API错误: {data}")
        else:
            print(f"✗ 服务器时间API失败: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 服务器时间测试失败: {e}")

if __name__ == "__main__":
    print("开始测试Bybit API...")
    test_public_endpoints()
    test_bybit_endpoints()
    
    print("\n\n建议:")
    print("1. 如果所有端点都返回403，可能是IP被限制")
    print("2. 可以尝试使用代理或更换网络环境")
    print("3. 或者暂时跳过Bybit数据，使用其他交易所数据")
