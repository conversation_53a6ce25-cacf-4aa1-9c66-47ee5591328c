#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正确的Huobi API域名和端点
"""

import requests
import time

def test_huobi_domains():
    """测试不同的Huobi域名"""
    
    # 可能的Huobi域名
    domains = [
        "https://api.huobi.pro",
        "https://api.hbdm.com",  # 合约专用域名
        "https://api.btcgateway.pro",  # 备用域名
        "https://api.huobi.de.com",  # 另一个域名
    ]
    
    # 测试symbol
    test_symbol = "BTC-USDT"
    
    # 可能的端点
    endpoints = [
        "/linear-swap-ex/market/detail/merged",
        "/swap-ex/market/detail/merged", 
        "/v1/contract_contract_info",
        "/heartbeat/",  # 测试连通性
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "application/json",
    }
    
    for domain in domains:
        print(f"\n测试域名: {domain}")
        print("=" * 50)
        
        # 先测试连通性
        try:
            response = requests.get(f"{domain}/heartbeat/", headers=headers, timeout=5)
            print(f"连通性测试: {response.status_code}")
        except Exception as e:
            print(f"连通性测试失败: {e}")
            continue
        
        # 测试各个端点
        for endpoint in endpoints:
            try:
                url = f"{domain}{endpoint}"
                
                if "contract_info" in endpoint:
                    # 获取合约信息，不需要参数
                    response = requests.get(url, headers=headers, timeout=10)
                else:
                    # 需要symbol参数
                    params = {"contract_code": test_symbol}
                    response = requests.get(url, params=params, headers=headers, timeout=10)
                
                print(f"  {endpoint}: {response.status_code}", end="")
                
                if response.status_code == 200:
                    data = response.json()
                    if "contract_info" in endpoint:
                        if data.get("status") == "ok":
                            print(" ✓ 合约信息API可用")
                        else:
                            print(f" ✗ {data}")
                    else:
                        if data.get("status") == "ok" and data.get("tick"):
                            print(" ✓ 市场数据API可用")
                        else:
                            print(f" ✗ {data.get('err-msg', data)}")
                else:
                    print(f" ✗ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"  {endpoint}: ✗ 异常 {e}")
                
            time.sleep(0.2)

def test_alternative_approach():
    """测试替代方案 - 使用现货API或其他方式"""
    print("\n\n测试替代方案...")
    print("=" * 50)
    
    # 尝试现货API获取24小时数据
    try:
        url = "https://api.huobi.pro/market/detail/merged"
        params = {"symbol": "btcusdt"}  # 现货格式
        
        response = requests.get(url, params=params, timeout=10)
        print(f"现货API测试: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "ok" and data.get("tick"):
                amount = data["tick"].get("amount", 0)
                print(f"✓ 现货24小时成交额: {amount}")
                print("建议：如果期货API不可用，可以考虑使用现货数据作为参考")
            else:
                print(f"✗ 现货API响应错误: {data}")
        else:
            print(f"✗ 现货API失败: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 现货API异常: {e}")

if __name__ == "__main__":
    print("开始测试正确的Huobi API...")
    test_huobi_domains()
    test_alternative_approach()
    
    print("\n\n建议解决方案:")
    print("1. 如果所有Huobi API都不可用，可以暂时跳过Huobi数据")
    print("2. 或者使用现货数据作为期货数据的近似值")
    print("3. 或者寻找其他提供Huobi数据的第三方API")
