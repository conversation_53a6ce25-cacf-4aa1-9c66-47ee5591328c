#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试combined_analysis.py的核心功能
"""

import sys
import os

# 添加analyze目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), "analyze"))


def test_imports():
    """测试导入是否正常"""
    try:
        # 先测试基本的Python模块
        import requests
        import json
        import time
        from decimal import Decimal
        import warnings
        import argparse
        import sys
        import os
        from datetime import datetime, timedelta
        import pickle

        print("✓ 基本模块导入成功")

        # 尝试导入pandas，如果失败就跳过
        try:
            import pandas as pd

            print("✓ pandas模块可用")
            pandas_available = True
        except ImportError:
            print("⚠ pandas模块不可用，将跳过相关功能")
            pandas_available = False

        # 现在尝试导入我们的模块
        if pandas_available:
            from combined_analysis import CombinedAnalyzer

            print("✓ 成功导入 CombinedAnalyzer")
        else:
            print("⚠ 由于pandas不可用，跳过CombinedAnalyzer导入测试")

        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_config():
    """测试配置是否正确"""
    try:
        # 检查pandas是否可用
        try:
            import pandas as pd

            pandas_available = True
        except ImportError:
            print("⚠ pandas不可用，跳过配置测试")
            return True

        from combined_analysis import CombinedAnalyzer

        # 测试默认配置
        analyzer = CombinedAnalyzer()
        config = analyzer.config

        # 检查新的配置项
        expected_keys = [
            "spot_volume_limit",
            "futures_volume_ratio",
            "depth_ratio_threshold",
            "spot_to_other_futures_ratio",
            "trade_balance_threshold",
            "enable_trade_balance_check",
            "enable_multi_exchange_check",
            "enable_net_flow_check",
            "enable_spot_other_futures_check",
            "min_exchange_count",
        ]

        for key in expected_keys:
            if key in config:
                print(f"✓ 配置项 {key}: {config[key]}")
            else:
                print(f"✗ 缺少配置项: {key}")
                return False

        # 检查默认值是否正确
        if config["spot_volume_limit"] == 8_000_000:
            print("✓ 现货交易量限制默认值正确: 8,000,000")
        else:
            print(f"✗ 现货交易量限制默认值错误: {config['spot_volume_limit']}")

        if config["futures_volume_ratio"] == 4.0:
            print("✓ 期货/现货交易量比例默认值正确: 4.0")
        else:
            print(f"✗ 期货/现货交易量比例默认值错误: {config['futures_volume_ratio']}")

        if config["spot_to_other_futures_ratio"] == 0.2:
            print("✓ 现货与非Binance期货比值默认值正确: 0.2")
        else:
            print(f"✗ 现货与非Binance期货比值默认值错误: {config['spot_to_other_futures_ratio']}")

        if config["min_exchange_count"] == 7:
            print("✓ 最小交易所数量默认值正确: 7")
        else:
            print(f"✗ 最小交易所数量默认值错误: {config['min_exchange_count']}")

        return True

    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False


def test_exchange_methods():
    """测试交易所方法是否存在"""
    try:
        from combined_analysis import CombinedAnalyzer

        analyzer = CombinedAnalyzer()

        # 检查新添加的方法
        methods = [
            "get_okx_24hr_ticker",
            "get_bybit_24hr_ticker",
            "get_bitget_24hr_ticker",
            "get_bingx_24hr_ticker",
            "get_huobi_24hr_ticker",
            "get_gate_24hr_ticker",
            "get_all_exchanges_24hr_volume",
        ]

        for method_name in methods:
            if hasattr(analyzer, method_name):
                print(f"✓ 方法存在: {method_name}")
            else:
                print(f"✗ 方法缺失: {method_name}")
                return False

        return True

    except Exception as e:
        print(f"✗ 方法测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 combined_analysis.py...")
    print("=" * 50)

    # 测试导入
    print("\n1. 测试导入...")
    if not test_imports():
        print("导入测试失败，停止测试")
        return False

    # 测试配置
    print("\n2. 测试配置...")
    if not test_config():
        print("配置测试失败")
        return False

    # 测试方法
    print("\n3. 测试方法...")
    if not test_exchange_methods():
        print("方法测试失败")
        return False

    print("\n" + "=" * 50)
    print("✓ 所有测试通过！")
    print("\n主要改进:")
    print("1. ✓ 现货交易量限制改为800万USDT")
    print("2. ✓ 期货/现货交易量比例改为400%")
    print("3. ✓ 添加了现货与非Binance期货交易量比值检查(20%以下)")
    print("4. ✓ 要求所有7个交易所都有期货交易")
    print("5. ✓ 删除了不需要的检查(BTC相关性、tick size占比等)")
    print("6. ✓ 添加了获取其他交易所期货交易量的功能")

    return True


if __name__ == "__main__":
    main()
