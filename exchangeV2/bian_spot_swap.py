from main.config import *

import hmac
import hashlib
import requests
import time
import json
import traceback        #输出报错
from urllib.parse import parse_qs, urlencode

from main.hanshu import *

class bian_spot_swap():

    def __init__(self, symbol='BTCUSDT', apiKey=0, ccyBi=''):
        keys = {
            '吃了吗资本': ['pIuA71b1oqwA6Gb3xWjBLvCVHaExHpeI2HQgzEyjC5MWjWdVSIC8lTs4rt3uL0qn', 'e8WuLsvjVWAVpexjphs4uKW5mDTqu6iGWM7FDzMKazf7U3DYrz4claRQidxTaNOj'],
        }

        self.url =  "https://api.binance.com"

        self.ws_url = "wss://fstream.binance.com/ws/"
        if apiKey in keys:
            apiKey = keys[apiKey].split('	') if type(keys[apiKey]) == str else keys[apiKey]
        else:
            apiKey = GetApiKey(apiKey, 'Bian')

        self.ApiKey = apiKey[0]
        self.SecretKEY = apiKey[1]

        self.symbol = symbol.split("_")[0]
        self.klines_dir = '/root/klines/'
        self.debug = 0
        self.spot = 1
        self.debugs = []

        """ 优先使用缓存数据"""
        self.delData = {}
        self.session = requests.Session()

        """ 缓存费率数据"""
        self.feilvData = {}

        """ 多出口IP"""
        try:
            ips = DuoIp
        except:
            ips = []

        self.i = 0
        self.ips = []
        if len(ips) > 1:
            log('币安 使用多IP获取行情', ips)
            for ip in ips:
                from requests_toolbelt.adapters import source  #指定出口IP
                sb = requests.Session()
                new_source = source.SourceAddressAdapter(ip)
                sb.mount('http://', new_source)
                sb.mount('https://', new_source)
                self.ips.append(sb)

        # else:
        #     log('Error: 币安 没有多IP', ips)




    def hashing(self,query_string):
        return hmac.new(self.SecretKEY.encode('utf-8'), query_string.encode('utf-8'), hashlib.sha256).hexdigest()

    def get_timestamp(self):
        return int(time.time() * 1000)

    def dispatch_request(self, http_method):
        self.session.headers.update({
            'Content-Type': 'application/json;charset=utf-8',
            'X-MBX-APIKEY': self.ApiKey
        })
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(http_method, 'GET')


    # 请求API
    def go(self, http_method, url_path, payload={},url=0, needKey=1):

        if self.ips:
            self.session = self.ips[self.i]
            # print(self.i)
            self.i += 1
            if self.i >= len(self.ips):
                self.i = 0

        query_string = urlencode(payload)
        query_string = query_string.replace('%27', '%22')

        url2 = url
        if not url2:
            url2 = self.url

        if needKey:
            if query_string:
                query_string = "{}&timestamp={}".format(query_string, self.get_timestamp())
            else:
                query_string = 'timestamp={}'.format(self.get_timestamp())
            url2 += url_path + '?' + query_string + '&signature=' + self.hashing(query_string)
        else:
            url2 += url_path + '?' + query_string

        # print(url2)
        if self.debug:
            t = NowTime_ms()

        params = {'url': url2, 'params': {}, 'timeout': 5}
        try:
            response = self.dispatch_request(http_method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败',url2)
            time.sleep(1)
            return self.go(http_method,url_path, payload,url,needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(url_path, payload, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2


    # 获取手续费率
    def GetFeilv(self):
        data = self.go("GET","/api/v3/account")
        try:
            ok = StrBi(float(data['commissionRates']['taker']), 3)
            log("当前币安账户手续费", StrBi(float(data['commissionRates']['maker']), 3)+"/"+StrBi(float(data['commissionRates']['taker']), 3))
        except:
            log("获取手续费失败", data)
            time.sleep(60)
            return self.GetFeilv()

        return ok


    """ 获取所有交易对价格、挂单盘口"""
    def GetTickerAll(self):

        data = self.go("GET", "/api/v3/ticker/bookTicker", {}, needKey=0)

        if not data or type(data) != list:
            print('获取所有盘口失败', data)
            time.sleep(0.1)
            return self.GetTickerAll()

        if self.ips:
            self.session = self.ips[0]

        return data


    """撤销所有订单"""
    def DeleteAllOrder(self, symbol, msg='', zhucang=False):
        t = NowTime_ms()
        fh = self.go("DELETE","/sapi/v1/margin/openOrders",{'symbol': symbol, 'isIsolated': zhucang})

        log(symbol, "撤销所有挂单", msg, str(NowTime_ms()-t)+'ms')


    """撤销订单"""
    def DeleteOrder(self, symbol, orderId, zhucang=False):
        t = NowTime_ms()
        fh = self.go("DELETE","/sapi/v1/margin/order",{'symbol': symbol, 'orderId': orderId, 'isIsolated': zhucang})

        log(symbol, "撤销挂单", orderId, str(NowTime_ms()-t)+'ms')


    """下单
    GTC,IOC,FOK
    LIMIT 限价单
    MARKET 市价单
    STOP_LOSS 止损单
    STOP_LOSS_LIMIT 限价止损单
    TAKE_PROFIT 止盈单
    TAKE_PROFIT_LIMIT 限价止盈单
    LIMIT_MAKER 限价只挂单

    NO_SIDE_EFFECT: 普通交易订单;
    MARGIN_BUY: 自动借款交易订单;
    AUTO_REPAY: 自动还款交易订单.
    """
    def PostOrder(self, symbol, side, jiage, liang, type='normal', jiancang=0, msg2=''):

        jiage = str(jiage)
        liang = str(liang)
        jiazhi = float(liang)*float(jiage)
        liang2 = liang+U(jiazhi, kuo=1)

        t = NowTime_ms()

        post = {
            'symbol': symbol,  #交易对
            # 'isIsolated': zhucang,  #是否逐仓杠杆，"TRUE", "FALSE", 默认 "FALSE"
            'side': side,       #方向
            'quantity': liang, #数量
            # 'quoteOrderQty': liang, #USDT数量
            'newOrderRespType': 'ACK',
            'timeInForce': 'GTC',
            'sideEffectType': 'AUTO_REPAY' if jiancang else 'MARGIN_BUY',
        }
        if type == 'post_only':
            post['type'] = 'LIMIT_MAKER'
            del post['timeInForce']

        elif type in ['STOP_LOSS_LIMIT', 'STOP_LOSS', 'TAKE_PROFIT', 'TAKE_PROFIT_LIMIT']:
            post['stopPrice'] = jiage
            post['price'] = jiage
            post['type'] = type.upper()
            post['timeInForce'] = 'GTC'

        elif type == 'ioc':
            post['type'] = 'LIMIT'
            post['timeInForce'] = 'IOC'

        elif type == 'normal':
            post['type'] = 'MARKET'
            del post['timeInForce']

        elif type == 'limit':
            post['type'] = 'LIMIT'

        if type != 'normal':
            post['price'] = jiage


        msg = "币安现货杠杆　"+symbol+'　'+post['type']+'　'

        if jiancang:
            msg += '平空仓' if side == 'BUY' else '平多仓'
        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'

        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　减仓:"+str(jiancang)+"　Type:"+type


        if (not jiancang or not float(liang)) and jiage != '0' and jiazhi < 5.1:
            log(msg, Color(msg2, -1), '开仓下单价值太小，跳过')
            return 0, '跳过'

        orderId = 0
        yc = 0
        for x in range(2):
            order = self.go("POST", "/sapi/v1/margin/order", post)
            yc = NowTime_ms()-t

            msg = [msg, Color(msg2, 1), str(yc)+'ms']

            if not 'orderId' in order.keys():
                log(side, symbol, Color('套利币安下单失败', -1), post, order, msg2)
                time.sleep(0.1)

            else:
                orderId = order['orderId']
                break

        return orderId, msg


    """ 获取最大可借金额"""
    def GetKejie(self, bi=0):
        bi = bi if bi else CcyBi

        data = self.go('GET', '/sapi/v1/margin/maxBorrowable', {'asset': bi})
        if not data or 'amount' not in data:
            print('获取可借失败', data)
            return 0
            time.sleep(1)
            return self.GetKejie()

        return float(data['amount'])



    """ 获取持仓信息"""
    def GetPos(self):
        data = self.go("GET", "/sapi/v1/margin/account")

        # pprint(data)
        if data and 'userAssets' in data:
            okdata = []
            for v in data['userAssets']:
                if v['asset'] == CcyBi:
                    continue

                ok = {}
                ok['symbol'] = v['asset']+CcyBi
                ok['liang'] = abs(float(v['netAsset']))
                if ok['liang'] == 0:
                    continue

                ok['side'] = 'BUY' if float(v['netAsset']) > 0 else 'SELL' #持仓方向
                ok['side2'] = 'SELL' if float(v['netAsset']) > 0 else 'BUY' #卖出方向
                ok['jiage'] = 0
                ok['nowJiage'] = 0
                ok['yingkui'] = 0
                ok['bzj'] = 0
                ok['roe'] = 0
                ok['time'] = NowTime_ms()
                okdata.append(ok)

            return okdata

        else:
            log(Color("获取持仓失败", -1), data)
            time.sleep(3)
            return self.GetPos()



    def GetData(self, jsonPath, isFile=0):
        jsonPath = self.klines_dir+jsonPath
        if not os.path.exists(jsonPath):
            return []

        if isFile:
            return True

        with open(jsonPath) as file_obj:
            return json.load(file_obj)


    def SaveData(self, jsonPath, okdata):
        if not os.path.exists(self.klines_dir):
            os.makedirs(self.klines_dir)

        jsonPath = self.klines_dir+jsonPath
        with open(jsonPath,'w') as file_obj:
            json.dump(okdata,file_obj)


    """设置杠杆倍数"""
    def SetGangGan(self, symbol, beishu):
        return
        # beishu = str(int(beishu))
        # fh = self.go("POST","/sapi/v1/margin/max-leverage",{'maxLeverage': beishu})
        # log(symbol, '币安设置杠杠', beishu, fh)


    """ 获取可用余额"""
    def GetYuer(self, p=1, bi=0, jiekaun=0):
        if tlog('获取现货价格', '', 10, xs=0):
            self.tickData = self.GetTickerAll()

        data = self.go("GET", "/sapi/v1/margin/account")

        datas = []
        if data and 'userAssets' in data:
            ok = {
                'all': 0,
                'keyong': 0,
                'yingkui': 0,
                'jiekuan': 0,
            }
            for v in data['userAssets']:

                if jiekaun and abs(float(v['borrowed'])):
                    datas.append([v['asset'], abs(float(v['borrowed']))])
                    continue

                if v['asset'] == 'USDT':
                    ok['all'] += float(v['netAsset'])
                    # ok['all'] += abs(float(v['borrowed']) + float(v['netAsset']))
                    ok['keyong'] = abs(float(v['free']))
                    ok['jiekuan'] = abs(float(v['borrowed']))

                else:
                    for tick in self.tickData:
                        if tick['symbol'] == v['asset']+'USDT':
                            ok['all'] += float(v['netAsset']) * float(tick['askPrice'])
                            break



        else:
            log(Color("获取余额失败", -1), data)
            time.sleep(3)
            return self.GetYuer(p, bi)

        if jiekaun:
            return datas

        ok['all'] = Si(max(0.01, ok['all']), 4)
        ok['keyong'] = Si(max(0.01, ok['keyong']), 4)
        if p:
            log(f'币安现货杠杆余额', ok['all'], '可用', N(ok['keyong'], 4),
                 '持仓盈亏', N(ok['yingkui'], 4))

        return ok

    """ 还款"""
    def HuanKuan(self, liang, bi=''):
        bi = bi if bi else CcyBi
        params = {
            'asset':bi, 'amount': format(abs(liang), 'f'), "isIsolated": "FALSE", "type": "REPAY"
        }
        log("repay params: ", params)
        data = self.go("POST","/sapi/v1/margin/borrow-repay",params)
        log('币安杠杆还款', bi, '数量', liang, '结果', data)
        return data

    """ 借款"""
    def JieKuan(self, bi, liang):
        data = self.go("POST","/sapi/v1/margin/loan",{'asset':bi, 'amount':liang})
        log('币安杠杆借币', bi, '数量', liang, '结果', data)
        return 'tranId' in data

    """"true": 双向持仓模式；"false": 单向持仓模式"""
    def SetChicang(self,side=False):
        log("设置持仓", side)
        return self.go("POST","/fapi/v1/positionSide/dual", {'dualSidePosition':side})


    """"true": 开启站内提现；"false": 关闭站内提现"""
    def SetTixian(self,side=True):
        if side:
            log('开启站内提现')
            url = '/sapi/v1/account/enableFastWithdrawSwitch'
        else:
            log('关闭站内提现')
            url = '/sapi/v1/account/disableFastWithdrawSwitch'
        return self.go("POST", url, url='https://api.binance.com')


    """提现"""
    def Tixian(self, dizhi, liang, bi='', net='TRX'):
        bi = bi if bi else CcyBi
        data = self.go("POST","/sapi/v1/capital/withdraw/apply",{'coin':bi, 'address':dizhi, 'network': net, 'amount':liang, 'transactionFeeFlag': True}, url='https://api.binance.com')
        log('币安提现 地址', dizhi, '数量',liang, '币种', bi, '结果', data)
        return data


    """创建子账户"""
    def CreateNumber(self, username):
        data = self.go("POST","/sapi/v1/sub-account/virtualSubAccount", {'subAccountString':username}, url='https://api.binance.com')
        log('创建子账户', username, '返回', data)

        data = self.go("POST","/sapi/v1/sub-account/futures/enable", {'email': data['email']}, url='https://api.binance.com')
        return data


    """ 子母账户合约相互转账"""
    def ziTixian(self, fa, jie, liang, bi=''):
        bi = bi if bi else CcyBi
        data = {
            'fromEmail': fa,
            'toEmail': jie,
            'futuresType': '1',
            'asset': bi,
            'amount': liang,
        }
        data = self.go("POST","/sapi/v1/sub-account/futures/internalTransfer", data, url='https://api.binance.com')

        log('[集群均衡]', '发送者', fa, '接受者', jie, bi, liang, '结果', data, Color('', 1))
        return data


    """ 子母账户相互转账"""
    def ziHua(self, liang, faWallet, jieWallet, fa='', jie='', bi=''):

        for i in range(5):
            bi = bi if bi else CcyBi
            data = {
                'fromAccountType': faWallet,    #"SPOT","USDT_FUTURE","COIN_FUTURE","MARGIN"(Cross),"ISOLATED_MARGIN"
                'toAccountType': jieWallet,
                'asset': bi,
                'amount': liang,
            }
            if fa:
                data['fromEmail'] = fa
            if jie:
                data['toEmail'] = jie

            data = self.go("POST","/sapi/v1/sub-account/universalTransfer", data, url='https://api.binance.com')
            msg = ['[万向划转]', '发送者', fa, faWallet, '接受者', jie, bi, jieWallet, '币种', bi, liang, '结果', data]
            log(*msg)

            if 'tranId' in data:
                return data['tranId']

            uploadError('币安万向划转失败！'+str(msg))
            time.sleep(2)

        return 0


    """ 获取Userid"""
    def GetEmail(self):
        data = self.go("GET", "/api/spot/v1/account/getInfo")
        return data['data']['user_id']


    """获取当前挂单"""
    def GetOrders(self, symbol=0):
        symbol = self.symbol if not symbol else symbol
        data = self.go("GET","/fapi/v1/openOrders",{'symbol': symbol})
        return data

    """获取所有挂单"""
    def GetAllOrders(self):
        data = self.go("GET","/fapi/v1/openOrders")
        return data


    """合约最新价格"""
    def GetNowJiage(self, symbol=0):
        symbol = self.symbol if not symbol else symbol
        data = self.go("GET","/fapi/v1/ticker/price",{'symbol': symbol}, url='https://fapi.binance.com')
        if not isinstance(data, dict) or 'price' not in data:
            print(symbol, '获取最新价格失败',data)
            time.sleep(0.11)
            return self.GetNowJiage(symbol)
        return float(data['price'])


    """获取开平仓的价格"""
    def GetSideJiage(self, side, symbol=0):
        symbol = self.symbol if not symbol else symbol

        newDepth = self.GetDepth(10, symbol)
        jiage = newDepth['bids'][0][0] if side == 'BUY' else newDepth['asks'][0][0]
        return jiage

    """Websockt监听账户需要的Key"""
    def GetlistenKey(self, symbol):
        if symbol:
            data = self.go("POST","/sapi/v1/userDataStream/isolated", {'symbol': symbol})
        else:
            data = self.go("POST","/sapi/v1/userDataStream")

        if not data or 'listenKey' not in data.keys():
            log('获取listenKey失败',data)
            time.sleep(0.1)
            return self.GetlistenKey(symbol)
        return data['listenKey']

    """ 延长Key"""
    def PutlistenKey(self, key, symbol):
        if symbol:
            return self.go("PUT","/sapi/v1/userDataStream/isolated", {'symbol': symbol, 'listenKey': key})
        else:
            return self.go("PUT","/sapi/v1/userDataStream")


    """币安服务器时间"""
    def GetTime(self):
        return self.go('GET', '/fapi/v1/time')['serverTime']

    """充值地址"""
    def GetAddress(self, bi='', net='TRX'):
        bi = bi if bi else CcyBi
        data = self.go('GET', '/sapi/v1/capital/deposit/address', {'coin': bi, 'network': net}, url='https://api.binance.com')
        if 'address' in data:
            log(bi, net, '币安充值地址', data['address'])
            return data['address']

        else:
            log('获取地址失败', data)
            return ''

    """获取所有交易对信息"""
    def GetSymbols(self):
        data = self.go("GET","/api/v3/exchangeInfo", needKey=0)

        if not data or 'symbols' not in data.keys():
            log('获取交易对信息失败',data)
            time.sleep(10)
            return self.GetSymbols()

        return data['symbols']



    """获取现货资产"""
    def GetXianhuo(self, bi=0):
        bi = "USDT" if not bi else bi
        data = self.go("POST", "/sapi/v3/asset/getUserAsset", url='https://api.binance.com')

        no = 0
        if not data or type(data) != list:
            no = 1

        else:
            ok = 0
            for info in data:
                if info['asset'] == bi: #USDT
                    ok = info
                    break
            if not ok:
                no = 1

        if no:
            print(bi, '获取现货余额失败', data)
            return 0

        return float(ok['free'])


    """
        MAIN_C2C 现货钱包转向C2C钱包
        MAIN_UMFUTURE 现货钱包转向U本位合约钱包
        MAIN_CMFUTURE 现货钱包转向币本位合约钱包
        MAIN_MARGIN 现货钱包转向杠杆全仓钱包
        MAIN_MINING 现货钱包转向矿池钱包
        C2C_MAIN C2C钱包转向现货钱包
        C2C_UMFUTURE C2C钱包转向U本位合约钱包
        C2C_MINING C2C钱包转向矿池钱包
        UMFUTURE_MAIN U本位合约钱包转向现货钱包
        UMFUTURE_C2C U本位合约钱包转向C2C钱包
        UMFUTURE_MARGIN U本位合约钱包转向杠杆全仓钱包
        CMFUTURE_MAIN 币本位合约钱包转向现货钱包
        MARGIN_MAIN 杠杆全仓钱包转向现货钱包
        MARGIN_UMFUTURE 杠杆全仓钱包转向U本位合约钱包
        MINING_MAIN 矿池钱包转向现货钱包
        MINING_UMFUTURE 矿池钱包转向U本位合约钱包
        MINING_C2C 矿池钱包转向C2C钱包
        MARGIN_CMFUTURE 杠杆全仓钱包转向币本位合约钱包
        CMFUTURE_MARGIN 币本位合约钱包转向杠杆全仓钱包
        MARGIN_C2C 杠杆全仓钱包转向C2C钱包
        C2C_MARGIN C2C钱包转向杠杆全仓钱包
        MARGIN_MINING 杠杆全仓钱包转向矿池钱包
        MINING_MARGIN 矿池钱包转向杠杆全仓钱包
        MAIN_PAY 现货钱包转向支付钱包
        PAY_MAIN 支付钱包转向现货钱包
    """
    def Huazhuan(self, liang, lType='UMFUTURE_MAIN', bi=0):
        #liang = N(liang,4)
        bi = "USDT" if not bi else bi
        log('划转 数量',liang,'类型',lType, '币种', bi)
        post = {
            'type': lType,   #U本位合约钱包转向现货钱包
            'asset': bi,
            'amount': liang,
        }
        data = self.go("POST", "/sapi/v1/asset/transfer", post, 'https://api.binance.com')

        if data and 'tranId' in data:
            log(Color('划转成功', 1), data)
            return True
        else:
            log(Color('划转失败', -1), data)
            return False
