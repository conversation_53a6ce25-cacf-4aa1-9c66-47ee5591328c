# -*- coding: utf-8 -*-
import os
import time
import sys
import json
import uvloop
import traceback        #输出报错
from pprint import pprint
import threading    #多线程
import base64

from ws4py.client.threadedclient import WebSocketClient
dirpath = os.path.abspath('.')+'/main'
sys.path.append(dirpath)
print(dirpath)

from main.hanshu import *
from main.config import *
import main.config
from moneyV2.money import *
from moneyV2.table import *
from super.spot import Spot
from super.swap import Swap
from super.getswap import *
from moneyV2.config_Exchange import *
from super.time_range_counter import TimeRangeCounter

from main.api import api

class Zero():
    def __init__(self):
        self.pos = []
        self.usdt = 0.0001

    def GetPos(self):
        return []

    def main(self):
        return []

def GetConfig(data=''):

    #重复尝试上传十次，每次间隔1秒
    for x in range(10):
        try:
            post = {'data': ''}
            if data:
                post['data'] = base64.b64encode(json.dumps(data).encode())

            url = 'https://clm.nb8.net/api/CaoKong/config'
            fanhui = requests.post(url, data=post, timeout=10)
            fJson = fanhui.json()['data']
            return fJson

        except Exception as e:
            try:
                print(fanhui.text)
            except:
                pass
            traceback.print_exc()

        time.sleep(1)

    return False

config = GetConfig()
SYMBOl = config['symbol']
print(SYMBOl)
TMP_SYMBOL = SYMBOl


class kuaipao():


    def __init__(self):
        global SYMBOl

        self.time_range_counter = TimeRangeCounter()
        self.spot_taker_counter = TimeRangeCounter(30)

        # self.bnb.GetYuer(p=0, bi=SYMBOl)
        # input('')

        self.fuckbg = 0
        self.toSymbolFunc = toSymbol        #money文件里调用不了
        # self.closePosFunc = closePos        #money文件里调用不了

        bnb, ws, self.symbolFun, order_status, self.feilv_time = getExchange(Exchange)

        self.bnb = bnb(apiKey=Api_Key, ccyBi=CcyBi)
        if Name+'_lever' in config:
            self.bnb.GangGan = int(config[Name+'_lever'])

        self.symbol = self.symbolFun(self.bnb)
        self.bnb.symbol = self.symbol.copy()

        self.api = api(self.bnb, [], isBnb=0)
        # print(self.bnb.GetPos())
        if 'Bian' in Exchange:
            self.ws = ws(self.bnb, ccyBi=CcyBi, depathCallback=self.depathCallback, orderCallback=self.orderCallback)
        else:
            self.ws = ws(self.bnb, ccyBi=CcyBi, depathCallback=self.depathCallback)

        self.ws.pos = self.bnb.GetPos()

        self.loop = uvloop.new_event_loop()

        self.look = {}
        for s in self.symbol:
            if SYMBOl+CcyBi == toSymbol(s, noCcyBi=1):
                s2 = toSymbol(s)
                self.look = {f'{s2}': 0}
                SYMBOl = s
                self.symbol = self.symbol[s]
                if Exchange in ['BianSwap']:
                    self.bnb.SetChicang(False)

                if hasattr(self.bnb, 'DeleteAllOrder'):
                    self.bnb.DeleteAllOrder(SYMBOl)

                if hasattr(self.bnb, 'GetFeilv'):
                    self.bnb.GetFeilv()

                break

        else:
            log('未找到', SYMBOl)
            time.sleep(3)
            os._exit(0)


        #启动一个框架
        runWs(self, [SYMBOl], [])

        self.data = {}
        self.ganggan = 0
        self.max_ganggan = 0
        self.coinex = Zero()
        self.ws2 = Zero()

        """ 现货"""
        self.startJia = 0      #初始价格
        self.bili = 0      #初始价格
        self.limit_bili = 0      #挂单距离
        self.preJia = 0        #上次挂单价格
        self.preJia2 = 0        #上次记录比例价格
        self.preSide = 0        #上次挂单价格
        self.preLiang = 0        #上次挂单数量
        self.preOrderLiang = 0
        self.preTakerJia = 0   #上次挂单价格
        self.prePos = 0
        self.preTime = 0
        self.orderJia = 0
        self.changePosTj = 0
        self.kejie = 0          #可借币余额
        self.usdt = 0           #现货当前余额
        self.bnb_usdt = 0
        self.SujiTime = 1       #随机等待时间
        self.takerCount = 0     #第几次吃单
        self.side = 'stop'      #策略进行的操作
        self.order = 'Maker'
        self.stopOpen = 0       #建仓完毕后，此值改为1（禁止建仓），Web修改操作重置为0
        self.pos_limit = 0          #从Web获取持仓金额限制，从而决定挂单量
        self.error = 0
        self.orderId = []
        self.orderIdLiang = {}     #存储变化的量
        self.getMax = ['Bitget', 'Ku', 'Okx']
        self.depthTime = 0      #深度更新时间


        t = threading.Thread(target=self.buyBnb)
        t.setDaemon(True)
        t.start()


        t = threading.Thread(target=self.upConfig)
        t.setDaemon(True)
        t.start()

        t = threading.Thread(target=self.celue)
        t.setDaemon(True)
        t.start()


    """ 购买BNB线程"""
    def buyBnb(self):
        while True:
            try:
                bnb = 0
                jiazhi = 0

                if 'spot' in Name:
                    jiage = self.bnb.GetNowJiage('BNBUSDT')
                    bnb_pos = GetPos('BNBUSDT', self.ws.pos, 'BUY')
                    if bnb_pos:
                        bnb = bnb_pos['liang']
                        jiazhi = jiage*bnb

                    if self.ws.keyong > 1000 and jiazhi < 20:
                        usdt = float(self.bnb.GetXianhuo('USDT'))
                        self.bnb.Huazhuan(round(100.1 - usdt, 6), 'MARGIN_MAIN')
                        log('买bnb', self.bnb.go("POST", '/api/v3/order', {"symbol": 'BNBUSDT', 'side': "BUY", 'type': "MARKET" , 'quoteOrderQty': 100}, url='https://api.binance.com'))
                        time.sleep(1)

                        #留在现货里的BNB划转回去
                        yuerZiXianhuo = float(self.bnb.GetXianhuo("BNB"))
                        if yuerZiXianhuo > 0:
                            self.bnb.Huazhuan(yuerZiXianhuo, 'MAIN_MARGIN', 'BNB')

                        usdt = float(self.bnb.GetXianhuo("USDT"))
                        if usdt > 0 and usdt < 10:
                            self.bnb.Huazhuan(usdt, 'MAIN_MARGIN')

                elif 'swap' in Name:
                    jiage = self.bnb.GetNowJiage('BNBUSDT')
                    bnb = float(self.bnb.GetYuer(p=0, bi="BNB")['all'])
                    jiazhi = jiage*bnb

                    if self.ws.keyong > 1000 and jiazhi < 20:
                        usdt = float(self.bnb.GetXianhuo('USDT'))
                        self.bnb.Huazhuan(round(100.1 - usdt, 6))
                        log('买bnb', self.bnb.go("POST", '/api/v3/order', {"symbol": 'BNBUSDT', 'side': "BUY", 'type': "MARKET" , 'quoteOrderQty': 100}, url='https://api.binance.com'))
                        time.sleep(1)

                        #留在现货里的BNB划转回去
                        yuerZiXianhuo = float(self.bnb.GetXianhuo("BNB"))
                        if yuerZiXianhuo > 0:
                            self.bnb.Huazhuan(yuerZiXianhuo, 'MAIN_UMFUTURE', 'BNB')

                        usdt = float(self.bnb.GetXianhuo("USDT"))
                        if usdt > 0 and usdt < 10:
                            self.bnb.Huazhuan(usdt, 'MAIN_UMFUTURE')

                if bnb:
                    tlog('BNB价值', U(jiazhi), 30*60)
                    self.bnb_usdt = jiage * bnb

                else:
                    self.bnb_usdt = 0

                time.sleep(10)
            except Exception as e:
                uploadError(traceback.format_exc())


    """ 订单更新"""
    def orderCallback(self, data):
        if data['liang']:
            if data['id'] not in self.orderId:
                return log('未知订单成交!!!!!')

            if 'swap' in Name:
                from super.swap import DeleteOrder
            if 'spot' in Name:
                from super.spot import DeleteOrder

            DeleteOrder(self, SYMBOl, '订单触发回调!!!!!!!!')
            tlog('差点成交了!!!!!', ['订单部分成交!!!'], 3)


    """ Ws更新"""
    def depathCallback(self, symbolWs, data, isbnb=0):
        s = toSymbol(symbolWs)

        if self.look[s]:
            return tlog('处理中', [s, '跳过...', Color('', 1)], 60 * 60)

        self.look[s] = 1
        self.depthTime = NowTime_ms()

        if 'spot' in Name:
            Spot(self, SYMBOl, data)

        elif 'swap' in Name:
            Swap(self, SYMBOl, data)

        else:
            if Exchange in self.getMax:
                jiage = N((data['bidPx']+data['askPx']) * 0.5, self.symbol['J'])
                data['maxLiang'] = N(self.getMaxPos(SYMBOl, jiage=jiage) / jiage, self.symbol['L'])
                if 'M' not in self.symbol.keys():
                    self.symbol['M'] = data['maxLiang']  #最大下单

            Getswap(self, SYMBOl, data)

        self.look[s] = 0



    def upYuer(self):
        """ Ws只有可用余额，就得用rest获取可用余额"""
        if 'spot' in Name:
            yuer = self.bnb.GetYuer(p=0, bi=SYMBOl)
        else:
            yuer = self.bnb.GetYuer(p=0)

        if yuer:
            self.ws.usdt = yuer['all']
            self.ws.keyong = yuer['keyong']
            self.ws.yingkui = yuer['yingkui']


    """ 定时更新余额持仓和线程"""
    async def _update_account(self):
        log('[线程]', '更新持仓和余额启动...')

        self.tables =  {
            "title" : Exchange,
            "cols" : [
                '交易对',
                '买一 | 卖一', '数量',
                'Z', "开仓价", '平仓价'
                '盈亏',
                '开仓时间',
            ],
            "rows" : [],
        }
        self.posTime = 0
        while True:
            try:
                self.upYuer()

                if NowTime_ms() - self.posTime > 5 * 1000:
                    self.api.SaveJson()
                    upPos(self, self.bnb, self.ws, self)

                # posAll = []
                # for pos in self.ws.pos:
                #     pass
                if tlog('释放内存', '', 5*60):

                    #释放占用的内存 buff/cache
                    os.system('echo 1 > /proc/sys/vm/drop_caches')
                    os.system('echo 2 > /proc/sys/vm/drop_caches')
                    os.system('echo 3 > /proc/sys/vm/drop_caches')

                await asyncio.sleep(1)

            except Exception as e:
                uploadError(traceback.format_exc())
                os._exit(0)


    """ 获取最大最大可开数量"""
    def getMaxPos(self, symbol, jiage=1):

        if self.symbol['maxJiazhi'] == 0:
            if Exchange == 'Bitget' and self.ganggan:
                self.symbol['maxJiazhi'] = self.bnb.GetMaxPos(symbol, jiage, self.ws.usdt, self.ganggan)

            if Exchange == 'Ku':
                self.symbol['maxJiazhi'] = self.bnb.GetMaxPos(symbol)

            if Exchange == 'Okx':
                m, price = self.bnb.GetMaxPos(symbol)
                log('Okx', symbol, '最大可开', m, '面值', self.symbol['Z'], '价值', U(m*self.symbol['Z']*price))
                self.symbol['maxJiazhi'] = m * self.symbol['Z'] * price

        return self.symbol['maxJiazhi']


    """ 定时更新余额持仓和线程"""
    def celue(self):
        time.sleep(1)
        log('[线程]', '策略启动...')
        while True:
            data = getDepth(SYMBOl, self.ws.data)

            if data:

                """ 检查策略上次运行时间"""
                if NowTime_ms() - self.depthTime > 100:
                    self.depathCallback(SYMBOl, data)

            else:
                tlog('没有深度数据', data, 3)

            Sleep(100)


    """ 定时更新操作"""
    def upConfig(self):
        log('[线程]', '获取设置启动...')
        upData = 1
        preLever = 0
        while True:

            try:
                data = ''
                if upData:  #每两次传递一次数据
                    data = self.data
                    upData = 0
                else:
                    upData = 1

                data = GetConfig(data)
                if data:

                    if Name not in data:
                        log('未配置', Name, data)
                        time.sleep(5)
                        continue

                    if self.bili != abs(float(data['bili'])):
                        log(Name, '变化最大操控比例', self.bili, '->', abs(float(data['bili'])))
                        self.bili = abs(float(data['bili']))
                        self.stopOpen = 0

                    if self.limit_bili != abs(float(data['limit_bili'])):
                        log(Name, '变化挂单操控比例', self.limit_bili, '->', abs(float(data['limit_bili'])))
                        self.limit_bili = abs(float(data['limit_bili']))
                        self.stopOpen = 0

                    if Name != 'spot' and self.order != data['order']:
                        log(Name, '变化订单', self.order, '->', data['order'])
                        self.order = data['order']
                        self.preJia = 0
                        self.stopOpen = 0
                        if hasattr(self.bnb, 'DeleteAllOrder'):
                            self.bnb.DeleteAllOrder(SYMBOl)

                    if self.side != data[Name]:
                        log(Name, '变化状态', self.side, '->', data[Name])
                        self.side = data[Name]
                        self.stopOpen = 0
                        self.takerCount = 0
                        self.startJia = 0
                        if self.side == 'rebot':
                            self.side = 'stop'
                            os._exit(0)


                    if TMP_SYMBOL != data['symbol']:
                        log(Name, '交易对变化',  TMP_SYMBOL, '->', data['symbol'])
                        uploadLog(isExit=1)

                    if Name+'_lever' in data:
                        lever = int(data[Name+'_lever'])
                        self.bnb.GangGan = int(config[Name+'_lever'])
                    else:
                        lever = 0
                        tlog('未找到杠杆设置', lever, 3)

                    if Name+'_pos' in data:
                        limit = float(data[Name+'_pos'])
                    else:
                        limit = 0
                        tlog('未找到持仓设置', limit, 3)

                    if lever != preLever:

                        if Exchange == 'Huobi':
                            self.bnb.symbol = {SYMBOl: {'GangGan': lever}}

                        self.ganggan = lever


                        if hasattr(self.bnb, 'GetMaxGangGan'):
                            self.max_ganggan = self.bnb.GetMaxGangGan(SYMBOl)
                            dq = min(self.max_ganggan, lever)
                            fh = self.bnb.SetGangGan(SYMBOl, dq)
                            self.ganggan = dq

                        else:
                            if Exchange == 'Bybit':
                                self.bnb.SetGangGan(SYMBOl, self.symbol['GangGan'], self.symbol['riskId'])
                            else:
                                self.bnb.SetGangGan(SYMBOl, lever)

                        self.bnb.GangGan = lever
                        self.symbol = self.symbolFun(self.bnb)[SYMBOl]
                        if Exchange == 'Deepcoin':
                            self.symbol['M'] = fh
                            self.symbol['GangGan'] = dq

                        preLever = lever

                        if Exchange in self.getMax:
                            self.symbol['maxJiazhi'] = 0


                    if limit !=  self.pos_limit:
                        log(Name, '变化持仓限额', U(self.pos_limit), '->', U(limit), Color('', -1))
                        self.pos_limit = limit
                        self.stopOpen = 0

            except Exception as e:
                uploadError(traceback.format_exc())
                os._exit(0)

            Sleep(600)


    def main(self):

        self.loop.create_task(self._update_account())
        self.loop.run_forever()




def runMain():
    try:
        m = kuaipao()
        while 1:
            m.main()

    except Exception as e:
        uploadError(traceback.format_exc())

if __name__=='__main__':
    runMain()
