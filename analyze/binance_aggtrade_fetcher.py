#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance现货aggTrade数据获取脚本
获取指定交易对过去两个小时的聚合交易数据
"""

import json
import time
from datetime import datetime, timedelta
import argparse
import sys
import os
from typing import List, Dict, Optional
import csv

# 尝试导入requests，如果没有则使用urllib
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    import urllib.request
    import urllib.parse
    import urllib.error
    HAS_REQUESTS = False
    print("警告: 未安装requests库，使用urllib作为替代")

# 尝试导入pandas，如果没有则使用csv
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("警告: 未安装pandas库，将使用csv格式保存")


class BinanceAggTradeClient:
    """Binance aggTrade数据获取客户端"""

    def __init__(self):
        self.base_url = "https://api.binance.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        if HAS_REQUESTS:
            self.session = requests.Session()
            self.session.headers.update(self.headers)
        else:
            self.session = None

    def _make_request(self, url, params=None, timeout=10):
        """通用HTTP请求方法，支持requests和urllib"""
        if params:
            if HAS_REQUESTS:
                # 使用requests
                response = self.session.get(url, params=params, timeout=timeout)
                response.raise_for_status()
                return response.json()
            else:
                # 使用urllib
                if params:
                    query_string = urllib.parse.urlencode(params)
                    url = f"{url}?{query_string}"

                req = urllib.request.Request(url, headers=self.headers)
                with urllib.request.urlopen(req, timeout=timeout) as response:
                    data = response.read().decode('utf-8')
                    return json.loads(data)
        else:
            if HAS_REQUESTS:
                response = self.session.get(url, timeout=timeout)
                response.raise_for_status()
                return response.json()
            else:
                req = urllib.request.Request(url, headers=self.headers)
                with urllib.request.urlopen(req, timeout=timeout) as response:
                    data = response.read().decode('utf-8')
                    return json.loads(data)

    def get_server_time(self) -> int:
        """获取Binance服务器时间"""
        try:
            data = self._make_request(f"{self.base_url}/api/v3/time")
            return data['serverTime']
        except Exception as e:
            print(f"获取服务器时间失败: {e}")
            return int(time.time() * 1000)

    def validate_symbol(self, symbol: str) -> bool:
        """验证交易对是否存在"""
        try:
            data = self._make_request(f"{self.base_url}/api/v3/exchangeInfo")
            symbols = [s['symbol'] for s in data['symbols'] if s['status'] == 'TRADING']
            return symbol.upper() in symbols
        except Exception as e:
            print(f"验证交易对失败: {e}")
            return False

    def get_aggtrades(self, symbol: str, start_time: int, end_time: int, limit: int = 1000) -> List[Dict]:
        """
        获取aggTrade数据

        Args:
            symbol: 交易对，如 'BTCUSDT'
            start_time: 开始时间戳(毫秒)
            end_time: 结束时间戳(毫秒)
            limit: 每次请求的最大数量(最大1000)

        Returns:
            aggTrade数据列表
        """
        all_trades = []
        current_start = start_time

        while current_start < end_time:
            try:
                params = {
                    'symbol': symbol.upper(),
                    'startTime': current_start,
                    'endTime': end_time,
                    'limit': limit
                }

                print(f"正在获取 {symbol} 从 {datetime.fromtimestamp(current_start/1000)} 的数据...")

                trades = self._make_request(
                    f"{self.base_url}/api/v3/aggTrades",
                    params=params,
                    timeout=30
                )

                if not trades:
                    print("没有更多数据")
                    break

                all_trades.extend(trades)
                print(f"获取到 {len(trades)} 条交易记录")

                # 更新下次请求的开始时间
                last_trade_time = trades[-1]['T']
                if last_trade_time >= current_start:
                    current_start = last_trade_time + 1
                else:
                    break

                # 避免请求过于频繁
                time.sleep(0.1)

            except (urllib.error.URLError if not HAS_REQUESTS else requests.exceptions.RequestException) as e:
                print(f"请求失败: {e}")
                time.sleep(1)
                continue
            except Exception as e:
                print(f"处理数据时出错: {e}")
                break

        return all_trades

    def fetch_last_n_hours(self, symbol: str, n) -> List[Dict]:
        """获取过去2小时的aggTrade数据"""

        # 验证交易对
        if not self.validate_symbol(symbol):
            raise ValueError(f"交易对 {symbol} 不存在或未在交易中")

        # 计算时间范围
        server_time = self.get_server_time()
        end_time = server_time
        start_time = end_time - (n * 60 * 60 * 1000)

        print(f"开始获取 {symbol} 的aggTrade数据")
        print(f"时间范围: {datetime.fromtimestamp(start_time/1000)} 到 {datetime.fromtimestamp(end_time/1000)}")

        trades = self.get_aggtrades(symbol, start_time, end_time)

        print(f"总共获取到 {len(trades)} 条交易记录")
        return trades

    def save_to_csv(self, trades: List[Dict], symbol: str, output_dir: str = ".") -> str:
        """保存数据到CSV文件"""
        if not trades:
            print("没有数据可保存")
            return ""

        # 转换数据格式
        processed_data = []
        for trade in trades:
            processed_data.append({
                'aggTradeId': trade['a'],           # 聚合交易ID
                'price': float(trade['p']),         # 价格
                'quantity': float(trade['q']),      # 数量
                'firstTradeId': trade['f'],         # 第一个交易ID
                'lastTradeId': trade['l'],          # 最后一个交易ID
                'timestamp': trade['T'],            # 交易时间戳
                'datetime': datetime.fromtimestamp(trade['T']/1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                'isBuyerMaker': trade['m'],         # 是否买方挂单
                'value': float(trade['p']) * float(trade['q'])  # 交易金额
            })

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{symbol.lower()}_aggtrades_{timestamp}.csv"
        filepath = os.path.join(output_dir, filename)

        if HAS_PANDAS:
            # 使用pandas保存
            df = pd.DataFrame(processed_data)
            df.to_csv(filepath, index=False, encoding='utf-8')
        else:
            # 使用csv模块保存
            fieldnames = ['aggTradeId', 'price', 'quantity', 'firstTradeId', 'lastTradeId',
                         'timestamp', 'datetime', 'isBuyerMaker', 'value']

            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(processed_data)

        print(f"数据已保存到: {filepath}")

        # 打印统计信息
        prices = [row['price'] for row in processed_data]
        quantities = [row['quantity'] for row in processed_data]
        values = [row['value'] for row in processed_data]
        datetimes = [row['datetime'] for row in processed_data]

        print(f"\n数据统计:")
        print(f"交易对: {symbol}")
        print(f"记录数: {len(processed_data)}")
        print(f"时间范围: {min(datetimes)} 到 {max(datetimes)}")
        print(f"价格范围: {min(prices):.8f} - {max(prices):.8f}")
        print(f"总交易量: {sum(quantities):.8f}")
        print(f"总交易额: {sum(values):.2f} USDT")

        return filepath


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取Binance现货aggTrade数据')
    parser.add_argument('symbol', help='交易对，如: BTCUSDT')
    parser.add_argument('-o', '--output', default='.', help='输出目录 (默认: 当前目录)')
    parser.add_argument('-n', '--hours', type=int, default=2, help='获取过去多少小时的数据 (默认: 2小时)')

    args = parser.parse_args()

    try:
        # 创建客户端
        client = BinanceAggTradeClient()

        # 获取数据
        trades = client.fetch_last_n_hours(args.symbol, args.hours)

        if trades:
            # 保存数据
            client.save_to_csv(trades, args.symbol, args.output)
        else:
            print("未获取到任何数据")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
