#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量获取多个交易对的aggTrade数据
"""

import os
import time
from datetime import datetime
from binance_aggtrade_fetcher import BinanceAggTradeClient


def batch_fetch_aggtrades(symbols, output_dir="./data", delay=1):
    """
    批量获取多个交易对的aggTrade数据
    
    Args:
        symbols: 交易对列表
        output_dir: 输出目录
        delay: 每个请求之间的延时(秒)
    """
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
    
    # 创建客户端
    client = BinanceAggTradeClient()
    
    # 统计信息
    success_count = 0
    failed_symbols = []
    
    print(f"开始批量获取 {len(symbols)} 个交易对的数据...")
    print(f"输出目录: {output_dir}")
    print(f"请求间隔: {delay}秒")
    print("-" * 50)
    
    for i, symbol in enumerate(symbols, 1):
        try:
            print(f"\n[{i}/{len(symbols)}] 正在处理 {symbol}...")
            
            # 获取数据
            trades = client.fetch_last_2_hours(symbol)
            
            if trades:
                # 保存数据
                filepath = client.save_to_csv(trades, symbol, output_dir)
                print(f"✓ {symbol} 成功保存到: {os.path.basename(filepath)}")
                success_count += 1
            else:
                print(f"✗ {symbol} 未获取到数据")
                failed_symbols.append(symbol)
            
        except Exception as e:
            print(f"✗ {symbol} 处理失败: {e}")
            failed_symbols.append(symbol)
        
        # 延时避免请求过于频繁
        if i < len(symbols):  # 最后一个不需要延时
            time.sleep(delay)
    
    # 打印总结
    print("\n" + "=" * 50)
    print("批量获取完成!")
    print(f"成功: {success_count}/{len(symbols)}")
    print(f"失败: {len(failed_symbols)}/{len(symbols)}")
    
    if failed_symbols:
        print(f"失败的交易对: {', '.join(failed_symbols)}")
    
    print(f"数据保存在: {output_dir}")


def get_popular_symbols():
    """获取热门交易对列表"""
    return [
        "BTCUSDT",    # Bitcoin
        "ETHUSDT",    # Ethereum
        "BNBUSDT",    # Binance Coin
        "ADAUSDT",    # Cardano
        "SOLUSDT",    # Solana
        "XRPUSDT",    # Ripple
        "DOTUSDT",    # Polkadot
        "DOGEUSDT",   # Dogecoin
        "AVAXUSDT",   # Avalanche
        "SHIBUSDT",   # Shiba Inu
        "MATICUSDT",  # Polygon
        "LTCUSDT",    # Litecoin
        "UNIUSDT",    # Uniswap
        "LINKUSDT",   # Chainlink
        "ATOMUSDT",   # Cosmos
    ]


def get_defi_symbols():
    """获取DeFi相关交易对"""
    return [
        "UNIUSDT",    # Uniswap
        "AAVEUSDT",   # Aave
        "COMPUSDT",   # Compound
        "MKRUSDT",    # Maker
        "SUSHIUSDT",  # SushiSwap
        "CRVUSDT",    # Curve
        "1INCHUSDT",  # 1inch
        "YFIUSDT",    # yearn.finance
        "SNXUSDT",    # Synthetix
        "BALUSDT",    # Balancer
    ]


def get_layer1_symbols():
    """获取Layer1区块链相关交易对"""
    return [
        "BTCUSDT",    # Bitcoin
        "ETHUSDT",    # Ethereum
        "BNBUSDT",    # BNB Chain
        "ADAUSDT",    # Cardano
        "SOLUSDT",    # Solana
        "DOTUSDT",    # Polkadot
        "AVAXUSDT",   # Avalanche
        "ATOMUSDT",   # Cosmos
        "NEARUSDT",   # NEAR Protocol
        "ALGOUSDT",   # Algorand
        "FTMUSDT",    # Fantom
        "EGLDUSDT",   # MultiversX
    ]


def main():
    """主函数"""
    print("Binance aggTrade 批量获取工具")
    print("=" * 40)
    
    # 选择交易对集合
    print("请选择要获取的交易对集合:")
    print("1. 热门交易对 (15个)")
    print("2. DeFi相关 (10个)")
    print("3. Layer1区块链 (12个)")
    print("4. 自定义交易对")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        symbols = get_popular_symbols()
        print(f"选择了热门交易对: {', '.join(symbols)}")
    elif choice == "2":
        symbols = get_defi_symbols()
        print(f"选择了DeFi交易对: {', '.join(symbols)}")
    elif choice == "3":
        symbols = get_layer1_symbols()
        print(f"选择了Layer1交易对: {', '.join(symbols)}")
    elif choice == "4":
        symbols_input = input("请输入交易对，用逗号分隔 (如: BTCUSDT,ETHUSDT): ").strip()
        symbols = [s.strip().upper() for s in symbols_input.split(",") if s.strip()]
        if not symbols:
            print("未输入有效的交易对")
            return
        print(f"自定义交易对: {', '.join(symbols)}")
    else:
        print("无效选择")
        return
    
    # 设置输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = f"./aggtrade_data_{timestamp}"
    
    # 确认开始
    print(f"\n将获取 {len(symbols)} 个交易对的过去2小时aggTrade数据")
    print(f"数据将保存到: {output_dir}")
    
    confirm = input("\n确认开始? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("已取消")
        return
    
    # 开始批量获取
    batch_fetch_aggtrades(symbols, output_dir, delay=1)


if __name__ == "__main__":
    main()
