#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bitget合约历史数据获取工具
支持获取指定合约币种的orderbook和trades历史数据
"""

import requests
import json
import time
import csv
import os
import argparse
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd


class BitgetHistoricalDataFetcher:
    """Bitget历史数据获取器"""
    
    def __init__(self):
        self.base_url = "https://api.bitget.com"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def _make_request(self, url: str, params: dict = None, timeout: int = 10) -> Optional[dict]:
        """发起HTTP请求"""
        try:
            response = self.session.get(url, params=params, timeout=timeout)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == '00000':
                return data.get('data')
            else:
                print(f"API错误: {data.get('msg', 'Unknown error')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return None
    
    def get_server_time(self) -> int:
        """获取服务器时间"""
        try:
            url = f"{self.base_url}/api/v2/public/time"
            data = self._make_request(url)
            if data:
                return int(data)
            else:
                return int(time.time() * 1000)
        except:
            return int(time.time() * 1000)
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证交易对是否存在"""
        try:
            url = f"{self.base_url}/api/v2/mix/market/contracts"
            params = {'productType': 'USDT-FUTURES'}
            data = self._make_request(url, params)
            
            if data:
                symbols = [contract['symbol'] for contract in data]
                # Bitget合约格式通常是 BTCUSDT, ETHUSDT 等
                formatted_symbol = symbol.upper().replace('_UMCBL', '')
                return formatted_symbol in symbols
            return False
        except Exception as e:
            print(f"验证交易对失败: {e}")
            return False
    
    def get_orderbook_snapshot(self, symbol: str, limit: int = 100) -> Optional[dict]:
        """获取orderbook快照数据"""
        try:
            # 格式化symbol
            formatted_symbol = symbol.upper().replace('_UMCBL', '')
            
            url = f"{self.base_url}/api/v2/mix/market/orderbook"
            params = {
                'symbol': formatted_symbol,
                'limit': str(limit)
            }
            
            data = self._make_request(url, params)
            if data:
                return {
                    'symbol': formatted_symbol,
                    'timestamp': int(time.time() * 1000),
                    'bids': data.get('bids', []),
                    'asks': data.get('asks', []),
                    'ts': data.get('ts', int(time.time() * 1000))
                }
            return None
            
        except Exception as e:
            print(f"获取orderbook失败: {e}")
            return None
    
    def get_recent_trades(self, symbol: str, limit: int = 100) -> Optional[List[dict]]:
        """获取最近的交易数据"""
        try:
            # 格式化symbol
            formatted_symbol = symbol.upper().replace('_UMCBL', '')
            
            url = f"{self.base_url}/api/v2/mix/market/fills"
            params = {
                'symbol': formatted_symbol,
                'limit': str(limit)
            }
            
            data = self._make_request(url, params)
            if data:
                trades = []
                for trade in data:
                    trades.append({
                        'symbol': formatted_symbol,
                        'tradeId': trade.get('tradeId'),
                        'price': float(trade.get('price', 0)),
                        'size': float(trade.get('size', 0)),
                        'side': trade.get('side'),  # buy or sell
                        'timestamp': int(trade.get('ts', 0)),
                        'datetime': datetime.fromtimestamp(int(trade.get('ts', 0)) / 1000).strftime('%Y-%m-%d %H:%M:%S')
                    })
                return trades
            return None
            
        except Exception as e:
            print(f"获取交易数据失败: {e}")
            return None

    def get_kline_data(self, symbol: str, granularity: str = '1m', limit: int = 100, 
                       start_time: Optional[int] = None, end_time: Optional[int] = None) -> Optional[List[dict]]:
        """获取K线数据"""
        try:
            # 格式化symbol
            formatted_symbol = symbol.upper().replace('_UMCBL', '')
            
            url = f"{self.base_url}/api/v2/mix/market/candles"
            params = {
                'symbol': formatted_symbol,
                'granularity': granularity,
                'limit': str(limit)
            }
            
            if start_time:
                params['startTime'] = str(start_time)
            if end_time:
                params['endTime'] = str(end_time)
            
            data = self._make_request(url, params)
            if data:
                klines = []
                for kline in data:
                    klines.append({
                        'symbol': formatted_symbol,
                        'timestamp': int(kline[0]),
                        'datetime': datetime.fromtimestamp(int(kline[0]) / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                        'open': float(kline[1]),
                        'high': float(kline[2]),
                        'low': float(kline[3]),
                        'close': float(kline[4]),
                        'volume': float(kline[5]),
                        'quote_volume': float(kline[6]) if len(kline) > 6 else 0
                    })
                return klines
            return None
            
        except Exception as e:
            print(f"获取K线数据失败: {e}")
            return None

    def collect_historical_data(self, symbol: str, hours: int = 2, interval_seconds: int = 60) -> dict:
        """收集指定小时数的历史数据"""
        print(f"开始收集 {symbol} 过去 {hours} 小时的历史数据...")
        
        # 验证交易对
        if not self.validate_symbol(symbol):
            raise ValueError(f"交易对 {symbol} 不存在或未在交易中")
        
        # 计算时间范围
        end_time = int(time.time() * 1000)
        start_time = end_time - (hours * 60 * 60 * 1000)
        
        print(f"时间范围: {datetime.fromtimestamp(start_time/1000)} 到 {datetime.fromtimestamp(end_time/1000)}")
        
        # 收集数据
        all_orderbooks = []
        all_trades = []
        all_klines = []
        
        # 获取K线数据（用于历史价格参考）
        print("获取K线数据...")
        klines = self.get_kline_data(symbol, granularity='1m', limit=hours*60, 
                                   start_time=start_time, end_time=end_time)
        if klines:
            all_klines.extend(klines)
            print(f"获取到 {len(klines)} 条K线数据")
        
        # 获取最近的交易数据
        print("获取最近交易数据...")
        trades = self.get_recent_trades(symbol, limit=1000)
        if trades:
            all_trades.extend(trades)
            print(f"获取到 {len(trades)} 条交易数据")
        
        # 获取当前orderbook快照
        print("获取orderbook快照...")
        orderbook = self.get_orderbook_snapshot(symbol, limit=100)
        if orderbook:
            all_orderbooks.append(orderbook)
            print(f"获取到orderbook快照，买盘 {len(orderbook['bids'])} 档，卖盘 {len(orderbook['asks'])} 档")
        
        return {
            'symbol': symbol,
            'start_time': start_time,
            'end_time': end_time,
            'klines': all_klines,
            'trades': all_trades,
            'orderbooks': all_orderbooks
        }

    def save_to_csv(self, data: dict, output_dir: str = '.') -> None:
        """保存数据到CSV文件"""
        symbol = data['symbol']
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存K线数据
        if data['klines']:
            klines_file = os.path.join(output_dir, f"{symbol}_klines_{timestamp}.csv")
            df_klines = pd.DataFrame(data['klines'])
            df_klines.to_csv(klines_file, index=False, encoding='utf-8')
            print(f"K线数据已保存到: {klines_file}")
        
        # 保存交易数据
        if data['trades']:
            trades_file = os.path.join(output_dir, f"{symbol}_trades_{timestamp}.csv")
            df_trades = pd.DataFrame(data['trades'])
            df_trades.to_csv(trades_file, index=False, encoding='utf-8')
            print(f"交易数据已保存到: {trades_file}")
        
        # 保存orderbook数据
        if data['orderbooks']:
            orderbook_file = os.path.join(output_dir, f"{symbol}_orderbook_{timestamp}.csv")
            orderbook_data = []
            for ob in data['orderbooks']:
                # 处理买盘
                for i, bid in enumerate(ob['bids']):
                    orderbook_data.append({
                        'symbol': ob['symbol'],
                        'timestamp': ob['timestamp'],
                        'datetime': datetime.fromtimestamp(ob['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                        'side': 'bid',
                        'level': i + 1,
                        'price': float(bid[0]),
                        'size': float(bid[1])
                    })
                # 处理卖盘
                for i, ask in enumerate(ob['asks']):
                    orderbook_data.append({
                        'symbol': ob['symbol'],
                        'timestamp': ob['timestamp'],
                        'datetime': datetime.fromtimestamp(ob['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                        'side': 'ask',
                        'level': i + 1,
                        'price': float(ask[0]),
                        'size': float(ask[1])
                    })
            
            if orderbook_data:
                df_orderbook = pd.DataFrame(orderbook_data)
                df_orderbook.to_csv(orderbook_file, index=False, encoding='utf-8')
                print(f"Orderbook数据已保存到: {orderbook_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取Bitget合约历史数据')
    parser.add_argument('symbol', help='交易对，如: BTCUSDT 或 BTCUSDT_UMCBL')
    parser.add_argument('-o', '--output', default='.', help='输出目录 (默认: 当前目录)')
    parser.add_argument('-n', '--hours', type=int, default=2, help='获取过去多少小时的数据 (默认: 2小时)')
    parser.add_argument('-i', '--interval', type=int, default=60, help='数据收集间隔秒数 (默认: 60秒)')

    args = parser.parse_args()

    try:
        # 创建客户端
        client = BitgetHistoricalDataFetcher()

        # 获取数据
        data = client.collect_historical_data(args.symbol, args.hours, args.interval)

        if data['klines'] or data['trades'] or data['orderbooks']:
            # 保存数据
            client.save_to_csv(data, args.output)
            print(f"\n数据收集完成！")
            print(f"K线数据: {len(data['klines'])} 条")
            print(f"交易数据: {len(data['trades'])} 条")
            print(f"Orderbook快照: {len(data['orderbooks'])} 个")
        else:
            print("未获取到任何数据")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
