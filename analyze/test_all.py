#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证所有功能是否正常工作
"""

import os
import sys
import subprocess
from datetime import datetime

def test_data_fetcher():
    """测试数据拉取功能"""
    print("=== 测试数据拉取功能 ===")

    try:
        # 测试帮助信息
        result = subprocess.run([
            sys.executable, 'binance_data_fetcher.py', '--help'
        ], capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            print("✓ 数据拉取脚本帮助信息正常")
        else:
            print("✗ 数据拉取脚本帮助信息异常")
            return False

        # 测试数据拉取（小量数据）
        print("正在拉取测试数据...")
        result = subprocess.run([
            sys.executable, 'binance_data_fetcher.py',
            '--symbol', 'BTCUSDT', '--hours', '1', '--interval', '1m'
        ], capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print("✓ 数据拉取功能正常")
            return True
        else:
            print("✗ 数据拉取功能异常")
            print(f"错误信息: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("✗ 数据拉取超时")
        return False
    except Exception as e:
        print(f"✗ 数据拉取测试异常: {e}")
        return False

def test_data_files():
    """测试数据文件"""
    print("\n=== 测试数据文件 ===")

    data_dir = 'data'
    if not os.path.exists(data_dir):
        print("✗ 数据目录不存在")
        return False

    trades_files = [f for f in os.listdir(data_dir) if 'trades' in f and f.endswith('.csv')]
    klines_files = [f for f in os.listdir(data_dir) if 'klines' in f and f.endswith('.csv')]

    if trades_files and klines_files:
        print(f"✓ 找到交易数据文件: {len(trades_files)} 个")
        print(f"✓ 找到K线数据文件: {len(klines_files)} 个")

        # 检查最新文件
        latest_trades = sorted(trades_files)[-1]
        latest_klines = sorted(klines_files)[-1]

        trades_path = os.path.join(data_dir, latest_trades)
        klines_path = os.path.join(data_dir, latest_klines)

        if os.path.getsize(trades_path) > 0 and os.path.getsize(klines_path) > 0:
            print("✓ 数据文件大小正常")
            return True
        else:
            print("✗ 数据文件为空")
            return False
    else:
        print("✗ 未找到数据文件")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n=== 测试依赖包 ===")

    required_packages = [
        'requests', 'pandas', 'numpy', 'matplotlib', 'seaborn'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n缺少的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("✓ 所有依赖包已安装")
        return True

def test_example_script():
    """测试示例脚本"""
    print("\n=== 测试示例脚本 ===")

    if not os.path.exists('example_usage.py'):
        print("✗ 示例脚本不存在")
        return False

    try:
        # 运行示例脚本（不显示图表）
        result = subprocess.run([
            sys.executable, 'example_usage.py'
        ], capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            print("✓ 示例脚本运行正常")
            return True
        else:
            print("✗ 示例脚本运行异常")
            print(f"错误信息: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("✗ 示例脚本运行超时")
        return False
    except Exception as e:
        print(f"✗ 示例脚本测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试Binance交易数据与K线关系分析工具...\n")

    tests = [
        test_dependencies,
        test_data_fetcher,
        test_data_files,
        test_example_script
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")

    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")

    if passed == total:
        print("🎉 所有测试通过！工具可以正常使用。")
        print("\n使用建议:")
        print("1. 运行 'python binance_data_fetcher.py --help' 查看使用说明")
        print("2. 运行 'python example_usage.py' 查看示例")
        print("3. 使用 'jupyter notebook trade_kline_analysis.ipynb' 进行详细分析")
    else:
        print("❌ 部分测试失败，请检查错误信息并修复问题。")

if __name__ == "__main__":
    main()
