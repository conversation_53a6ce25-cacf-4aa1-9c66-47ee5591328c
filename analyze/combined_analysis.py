#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合分析脚本：按照filter_rule.txt规则进行筛选
0. okx，bybit，bitget，bingx，huobi，Gate, binance 中至少4个交易所有期货交易的品种
1. binance 现货交易量小于1000万USDT
2. binance 期货交易量是现货交易量的500%以上
3. binance tick size占比大于0.0001
5. 净流入时上涨，净流出时下跌，净流入计算方法：24小时内的trade，isBuyerMaker为true表示主动卖出，反之为主动买入，主动买入大于主动卖出为净流入，反之为净流出
6. binance 与BTC的相关性小于0.5
7. binance 期货的十档挂单量是现货的2倍以上，注意期货的tick size 和 现货的tick size可能不一样，如果不一样，需要换算成现货的tick size
8. 如果价格发生波动，那么成交量也是随之变化，即价格波动越剧烈，成交量也越大, 反之如果价格波动，但是成交量没有明显变化的就过滤掉
9. 近24小时的trade相对平衡，即isBuyerMaker true 和 false比例相对均衡
10. binance期货千三滑点，在okx，bybit，bitget，bingx，huobi，Gate等大的交易平台上的可开数量(USDT)也填到表格里
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from decimal import Decimal
import warnings
import argparse
import sys
import os
from datetime import datetime, timedelta
import pickle

warnings.filterwarnings("ignore")


class CombinedAnalyzer:
    def __init__(self, config=None):
        self.spot_base_url = "https://api.binance.com"
        self.futures_base_url = "https://fapi.binance.com"
        self.session = requests.Session()

        # 其他交易所API端点
        self.other_exchanges = {
            "OKX": "https://www.okx.com",
            "Bybit": "https://api.bybit.com",
            "Bitget": "https://api.bitget.com",
            "BingX": "https://open-api.bingx.com",
            "Huobi": "https://api.huobi.pro",
            "Gate": "https://api.gateio.ws",
        }

        # 缓存配置
        self.cache_dir = "cache"
        self.cache_file = os.path.join(self.cache_dir, "exchange_availability_cache.pkl")
        self.cache_expiry_days = 7

        # 确保缓存目录存在
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

        # 筛选参数配置
        if config is None:
            config = {}

        self.config = {
            "spot_volume_limit": config.get("spot_volume_limit", 8_000_000),  # 现货交易量限制(USDT) - 改为800万
            "futures_volume_ratio": config.get("futures_volume_ratio", 4.0),  # 期货/现货交易量比例 - 改为400%
            "depth_ratio_threshold": config.get("depth_ratio_threshold", 2.0),  # 期货/现货挂单量比例阈值
            "spot_to_other_futures_ratio": config.get(
                "spot_to_other_futures_ratio", 0.2
            ),  # 现货与非Binance期货交易量比值阈值 - 20%以下
            "trade_balance_threshold": config.get("trade_balance_threshold", 0.6),  # 交易平衡度阈值
            "enable_trade_balance_check": config.get("enable_trade_balance_check", True),  # 是否启用交易平衡检查
            "enable_multi_exchange_check": config.get("enable_multi_exchange_check", True),  # 是否启用多交易所检查
            "enable_net_flow_check": config.get("enable_net_flow_check", True),  # 是否启用净流入流出检查
            "enable_spot_other_futures_check": config.get(
                "enable_spot_other_futures_check", True
            ),  # 是否启用现货与非Binance期货比值检查
            "min_exchange_count": config.get(
                "min_exchange_count", 7
            ),  # 最小交易所数量要求 - 改为7个（所有交易所都要有）
        }

    def load_cache(self):
        """加载缓存数据"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, "rb") as f:
                    cache_data = pickle.load(f)

                # 检查缓存是否过期
                cache_time = cache_data.get("timestamp", datetime.min)
                if datetime.now() - cache_time < timedelta(days=self.cache_expiry_days):
                    print(f"✓ 加载缓存数据 (缓存时间: {cache_time.strftime('%Y-%m-%d %H:%M:%S')})")
                    return cache_data.get("data", {})
                else:
                    print(f"✗ 缓存已过期 (缓存时间: {cache_time.strftime('%Y-%m-%d %H:%M:%S')})")
                    return {}
            else:
                print("✗ 缓存文件不存在")
                return {}
        except Exception as e:
            print(f"✗ 加载缓存失败: {e}")
            return {}

    def save_cache(self, data):
        """保存缓存数据"""
        try:
            cache_data = {"timestamp": datetime.now(), "data": data}
            with open(self.cache_file, "wb") as f:
                pickle.dump(cache_data, f)
            print(f"✓ 缓存数据已保存到 {self.cache_file}")
        except Exception as e:
            print(f"✗ 保存缓存失败: {e}")

    def get_binance_symbols(self):
        """获取Binance现货和期货交易对信息"""
        print("获取Binance交易对信息...")

        # 获取基础数据
        spot_info = self.get_spot_exchange_info()
        futures_info = self.get_futures_exchange_info()

        if not all([spot_info, futures_info]):
            return set(), {}, {}

        # 处理现货数据
        spot_symbols = {}
        for symbol_info in spot_info["symbols"]:
            if symbol_info["status"] == "TRADING":
                symbol = symbol_info["symbol"]
                tick_size = self.parse_tick_size(symbol_info["filters"])
                if tick_size:
                    spot_symbols[symbol] = {
                        "tick_size": tick_size,
                        "base_asset": symbol_info["baseAsset"],
                        "quote_asset": symbol_info["quoteAsset"],
                    }

        # 处理期货数据
        futures_symbols = {}
        for symbol_info in futures_info["symbols"]:
            if symbol_info["status"] == "TRADING" and symbol_info["contractType"] == "PERPETUAL":
                symbol = symbol_info["symbol"]
                tick_size = self.parse_tick_size(symbol_info["filters"])
                if tick_size:
                    futures_symbols[symbol] = {
                        "tick_size": tick_size,
                        "base_asset": symbol_info["baseAsset"],
                        "quote_asset": symbol_info["quoteAsset"],
                    }

        # 找出既有现货又有期货的交易对
        common_symbols = set(spot_symbols.keys()) & set(futures_symbols.keys())

        print(f"现货交易对: {len(spot_symbols)}, 期货交易对: {len(futures_symbols)}")
        print(f"既有现货又有期货的交易对: {len(common_symbols)}")

        return common_symbols, spot_symbols, futures_symbols

    def get_spot_exchange_info(self):
        """获取现货交易对信息"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/exchangeInfo"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取现货交易对信息失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_futures_exchange_info(self):
        """获取期货交易对信息"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/exchangeInfo"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取期货交易对信息失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_spot_24hr_ticker(self):
        """获取现货24小时价格变动统计"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/ticker/24hr"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取现货24小时统计失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_futures_24hr_ticker(self):
        """获取期货24小时价格变动统计"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/ticker/24hr"
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取期货24小时统计失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(2)
        return None

    def get_spot_depth(self, symbol, limit=10):
        """获取现货深度数据"""
        for attempt in range(3):
            try:
                url = f"{self.spot_base_url}/api/v3/depth"
                params = {"symbol": symbol, "limit": limit}
                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} 现货深度失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_futures_depth(self, symbol, limit=10):
        """获取期货深度数据"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/depth"
                params = {"symbol": symbol, "limit": limit}
                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} 期货深度失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_okx_depth(self, symbol, limit=10):
        """获取OKX深度数据"""
        for attempt in range(3):
            try:
                # 转换symbol格式：BTCUSDT -> BTC-USDT-SWAP (永续合约)
                base_symbol = symbol[:-4]  # 去掉USDT
                okx_symbol = f"{base_symbol}-USDT-SWAP"
                url = "https://www.okx.com/api/v5/market/books"
                params = {"instId": okx_symbol, "sz": limit}

                # 添加请求头
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                response = self.session.get(url, params=params, headers=headers, timeout=15)
                response.raise_for_status()
                data = response.json()

                if data.get("code") == "0" and data.get("data") and len(data["data"]) > 0:
                    return data["data"][0]
                elif data.get("code") != "0":
                    print(f"  OKX API错误: {data.get('msg', 'Unknown error')}")
                    return None

            except Exception as e:
                print(f"  OKX深度获取失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_bybit_depth(self, symbol, limit=10):
        """获取Bybit深度数据"""
        try:
            url = f"{self.other_exchanges['Bybit']}/v5/market/orderbook"
            params = {"category": "linear", "symbol": symbol, "limit": limit}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("retCode") == 0 and data.get("result"):
                return data["result"]
        except Exception as e:
            print(f"  Bybit深度获取失败: {e}")
        return None

    def get_bitget_depth(self, symbol, limit=10):
        """获取Bitget深度数据"""
        try:
            # Bitget的永续合约格式
            bitget_symbol = f"{symbol}_UMCBL"
            url = f"{self.other_exchanges['Bitget']}/api/mix/v1/market/depth"
            params = {"symbol": bitget_symbol, "limit": limit}
            response = self.session.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "00000" and data.get("data"):
                    return data["data"]
        except Exception:
            pass  # 静默处理Bitget错误
        return None

    def get_bingx_depth(self, symbol, limit=10):
        """获取BingX深度数据"""
        for attempt in range(3):
            try:
                # 转换symbol格式：NEWTUSDT -> NEWT-USDT
                bingx_symbol = f"{symbol[:-4]}-{symbol[-4:]}"
                url = f"{self.other_exchanges['BingX']}/openApi/swap/v2/quote/depth"
                params = {"symbol": bingx_symbol, "limit": limit}

                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                response = self.session.get(url, params=params, headers=headers, timeout=15)
                response.raise_for_status()
                data = response.json()

                if data.get("code") == 0 and data.get("data"):
                    return data["data"]
                elif data.get("code") != 0:
                    print(f"  BingX API错误: {data.get('msg', 'Unknown error')}")
                    return None

            except Exception as e:
                print(f"  BingX深度获取失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_huobi_depth(self, symbol, limit=10):
        """获取Huobi深度数据"""
        try:
            # 转换symbol格式：BTCUSDT -> btcusdt
            huobi_symbol = symbol.lower()
            url = f"{self.other_exchanges['Huobi']}/market/depth"
            params = {"symbol": huobi_symbol, "depth": limit, "type": "step0"}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get("status") == "ok" and data.get("tick"):
                return data["tick"]
        except Exception as e:
            print(f"  Huobi深度获取失败: {e}")
        return None

    def get_gate_depth(self, symbol, limit=10):
        """获取Gate深度数据"""
        for attempt in range(3):
            try:
                # 转换symbol格式：NEWTUSDT -> NEWT_USDT
                gate_symbol = f"{symbol[:-4]}_{symbol[-4:]}"
                url = f"{self.other_exchanges['Gate']}/api/v4/futures/usdt/order_book"
                params = {"contract": gate_symbol, "limit": limit}

                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                response = self.session.get(url, params=params, headers=headers, timeout=15)
                response.raise_for_status()
                data = response.json()

                if "bids" in data and "asks" in data:
                    # 转换Gate的数据格式为标准格式
                    # Gate格式: [{"s": 507, "p": "0.3817"}, ...]
                    # 标准格式: [["0.3817", "507"], ...]

                    standard_bids = []
                    for bid in data["bids"]:
                        standard_bids.append([bid["p"], str(bid["s"])])

                    standard_asks = []
                    for ask in data["asks"]:
                        standard_asks.append([ask["p"], str(ask["s"])])

                    return {"bids": standard_bids, "asks": standard_asks}
                elif "message" in data:
                    print(f"  Gate API错误: {data['message']}")
                    return None

            except Exception as e:
                print(f"  Gate深度获取失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_okx_24hr_ticker(self, symbol):
        """获取OKX 24小时期货交易量数据"""
        for attempt in range(3):
            try:
                # 转换symbol格式：BTCUSDT -> BTC-USDT-SWAP
                base_symbol = symbol[:-4]
                okx_symbol = f"{base_symbol}-USDT-SWAP"
                url = "https://www.okx.com/api/v5/market/ticker"
                params = {"instId": okx_symbol}

                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                response = self.session.get(url, params=params, headers=headers, timeout=15)
                response.raise_for_status()
                data = response.json()

                if data.get("code") == "0" and data.get("data") and len(data["data"]) > 0:
                    ticker_data = data["data"][0]
                    # OKX返回的vol24h是以币为单位，volCcy24h是以USDT为单位
                    return float(ticker_data.get("volCcy24h", 0))
                elif data.get("code") != "0":
                    print(f"  OKX API错误: {data.get('msg', 'Unknown error')}")
                    return 0

            except Exception as e:
                print(f"  OKX 24小时数据获取失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return 0

    def get_bybit_24hr_ticker(self, symbol):
        """获取Bybit 24小时期货交易量数据"""
        try:
            url = f"{self.other_exchanges['Bybit']}/v5/market/tickers"
            params = {"category": "linear", "symbol": symbol}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("retCode") == 0 and data.get("result") and data.get("result", {}).get("list"):
                ticker_data = data["result"]["list"][0]
                # Bybit的turnover24h是24小时成交额(USDT)
                return float(ticker_data.get("turnover24h", 0))
        except Exception as e:
            print(f"  Bybit 24小时数据获取失败: {e}")
        return 0

    def get_bitget_24hr_ticker(self, symbol):
        """获取Bitget 24小时期货交易量数据"""
        try:
            # Bitget的永续合约格式
            bitget_symbol = f"{symbol}_UMCBL"
            url = f"{self.other_exchanges['Bitget']}/api/mix/v1/market/ticker"
            params = {"symbol": bitget_symbol}
            response = self.session.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "00000" and data.get("data"):
                    # Bitget的usdtVolume是24小时成交额(USDT)
                    return float(data["data"].get("usdtVolume", 0))
        except Exception:
            pass  # 静默处理Bitget错误
        return 0

    def get_bingx_24hr_ticker(self, symbol):
        """获取BingX 24小时期货交易量数据"""
        for attempt in range(3):
            try:
                # 转换symbol格式：NEWTUSDT -> NEWT-USDT
                bingx_symbol = f"{symbol[:-4]}-{symbol[-4:]}"
                url = f"{self.other_exchanges['BingX']}/openApi/swap/v2/quote/ticker"
                params = {"symbol": bingx_symbol}

                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                response = self.session.get(url, params=params, headers=headers, timeout=15)
                response.raise_for_status()
                data = response.json()

                if data.get("code") == 0 and data.get("data"):
                    # BingX的turnover是24小时成交额(USDT)
                    return float(data["data"].get("turnover", 0))
                elif data.get("code") != 0:
                    print(f"  BingX API错误: {data.get('msg', 'Unknown error')}")
                    return 0

            except Exception as e:
                print(f"  BingX 24小时数据获取失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return 0

    def get_huobi_24hr_ticker(self, symbol):
        """获取Huobi 24小时期货交易量数据"""
        try:
            # 转换symbol格式：BTCUSDT -> btcusdt
            huobi_symbol = symbol.lower()
            url = f"{self.other_exchanges['Huobi']}/linear-swap-ex/market/detail/merged"
            params = {"contract_code": huobi_symbol}
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("status") == "ok" and data.get("tick"):
                # Huobi的amount是24小时成交额(USDT)
                return float(data["tick"].get("amount", 0))
        except Exception as e:
            print(f"  Huobi 24小时数据获取失败: {e}")
        return 0

    def get_gate_24hr_ticker(self, symbol):
        """获取Gate 24小时期货交易量数据"""
        for attempt in range(3):
            try:
                # 转换symbol格式：NEWTUSDT -> NEWT_USDT
                gate_symbol = f"{symbol[:-4]}_{symbol[-4:]}"
                url = f"{self.other_exchanges['Gate']}/api/v4/futures/usdt/tickers"
                params = {"contract": gate_symbol}

                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                response = self.session.get(url, params=params, headers=headers, timeout=15)
                response.raise_for_status()
                data = response.json()

                if isinstance(data, list) and len(data) > 0:
                    ticker_data = data[0]
                    # Gate的volume_24h_quote是24小时成交额(USDT)
                    return float(ticker_data.get("volume_24h_quote", 0))
                elif isinstance(data, dict) and "message" in data:
                    print(f"  Gate API错误: {data['message']}")
                    return 0

            except Exception as e:
                print(f"  Gate 24小时数据获取失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return 0

    def get_all_exchanges_24hr_volume(self, symbol):
        """
        获取所有其他交易所的24小时期货交易量(USDT)
        返回: {exchange_name: volume_usdt}
        """
        print(f"  获取其他交易所 {symbol} 24小时期货交易量...")
        results = {}

        # 获取各交易所24小时数据
        exchanges_methods = {
            "OKX": self.get_okx_24hr_ticker,
            "Bybit": self.get_bybit_24hr_ticker,
            "Bitget": self.get_bitget_24hr_ticker,
            "BingX": self.get_bingx_24hr_ticker,
            "Huobi": self.get_huobi_24hr_ticker,
            "Gate": self.get_gate_24hr_ticker,
        }

        for exchange, method in exchanges_methods.items():
            try:
                volume = method(symbol)
                results[exchange] = volume
                if volume > 0:
                    print(f"    {exchange}: {self.format_volume(volume)}")
                else:
                    print(f"    {exchange}: N/A")
            except Exception as e:
                print(f"    {exchange}: 获取失败 - {e}")
                results[exchange] = 0

        return results

    def get_kline_data(self, symbol, interval="1m", limit=1440):
        """获取K线数据 (1440分钟 = 24小时)"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/klines"
                params = {"symbol": symbol, "interval": interval, "limit": limit}
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} K线数据失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_recent_trades(self, symbol, limit=1000):
        """获取最近的交易数据来分析买卖方向平衡"""
        for attempt in range(3):
            try:
                url = f"{self.futures_base_url}/fapi/v1/aggTrades"
                params = {"symbol": symbol, "limit": limit}
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                return response.json()
            except Exception as e:
                print(f"获取 {symbol} 交易数据失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:
                    time.sleep(1)
        return None

    def get_24h_trades(self, symbol):
        """获取最近24小时的交易数据"""
        try:
            # 计算24小时前的时间戳
            current_time = int(time.time() * 1000)
            start_time = current_time - 24 * 60 * 60 * 1000  # 24小时前

            all_trades = []
            from_id = None

            # 分批获取24小时内的所有交易数据
            for batch in range(10):  # 最多获取10批，避免无限循环
                try:
                    url = f"{self.futures_base_url}/fapi/v1/aggTrades"
                    params = {
                        "symbol": symbol,
                        "startTime": start_time,
                        "endTime": current_time,
                        "limit": 1000,  # 每批最多1000条
                    }

                    if from_id:
                        params["fromId"] = from_id

                    response = self.session.get(url, params=params, timeout=30)
                    response.raise_for_status()
                    trades = response.json()

                    if not trades:
                        break

                    all_trades.extend(trades)

                    # 如果返回的交易数量少于1000，说明已经获取完所有数据
                    if len(trades) < 1000:
                        break

                    # 设置下一批的起始ID
                    from_id = trades[-1]["a"] + 1

                    # 避免请求过于频繁
                    time.sleep(0.1)

                except Exception as e:
                    print(f"  获取第{batch+1}批交易数据失败: {e}")
                    break

            print(f"  获取到 {len(all_trades)} 条24小时交易数据")
            return all_trades

        except Exception as e:
            print(f"  获取24小时交易数据失败: {e}")
            # 如果24小时数据获取失败，回退到最近1000条交易
            return self.get_recent_trades(symbol, 1000)

    def parse_tick_size(self, filters):
        """解析价格精度(tick size)"""
        for filter_item in filters:
            if filter_item["filterType"] == "PRICE_FILTER":
                return float(filter_item["tickSize"])
        return None

    def calculate_tick_ratio(self, tick_size, last_price):
        """计算tick size / last price比例"""
        if last_price == 0:
            return 0
        return tick_size / last_price

    def format_volume(self, volume):
        """格式化交易量为M/B单位"""
        if volume >= 1_000_000_000:
            return f"{volume / 1_000_000_000:.3f}B"
        elif volume >= 1_000_000:
            return f"{volume / 1_000_000:.3f}M"
        else:
            return f"{volume:.0f}"

    def calculate_depth_quantity(self, depth_data, tick_size, price_range_pct=0.003):
        """
        计算深度数据中指定价格范围内的挂单量
        price_range_pct: 价格范围百分比，默认0.3%（千三滑点）
        """
        if not depth_data or "bids" not in depth_data or "asks" not in depth_data:
            return 0, 0

        bids = depth_data["bids"]
        asks = depth_data["asks"]

        if not bids or not asks:
            return 0, 0

        # 获取最佳买卖价
        best_bid = float(bids[0][0])
        best_ask = float(asks[0][0])
        mid_price = (best_bid + best_ask) / 2

        # 计算价格范围
        price_range = mid_price * price_range_pct
        bid_threshold = mid_price - price_range
        ask_threshold = mid_price + price_range

        # 计算买单量（USDT金额）
        bid_quantity_usdt = 0
        for price_str, qty_str in bids:
            price = float(price_str)
            qty = float(qty_str)
            if price >= bid_threshold:
                bid_quantity_usdt += price * qty  # 转换为USDT金额
            else:
                break

        # 计算卖单量（USDT金额）
        ask_quantity_usdt = 0
        for price_str, qty_str in asks:
            price = float(price_str)
            qty = float(qty_str)
            if price <= ask_threshold:
                ask_quantity_usdt += price * qty  # 转换为USDT金额
            else:
                break

        return bid_quantity_usdt, ask_quantity_usdt

    def normalize_tick_quantity(self, quantity_usdt, from_tick, to_tick, price):
        """
        将挂单量USDT金额从一个tick size标准化到另一个tick size
        由于我们现在使用USDT金额，tick size差异的影响已经在价格中体现，
        所以不需要额外调整
        """
        # USDT金额不需要tick size调整
        return quantity_usdt

    def get_all_exchanges_depth(self, symbol, current_price):
        """
        获取所有交易所千三滑点内的可开数量(USDT)
        current_price: 当前价格，用于计算千三滑点范围
        """
        results = {}

        # 获取各交易所深度数据
        exchanges_data = {
            "OKX": self.get_okx_depth(symbol),
            "Bybit": self.get_bybit_depth(symbol),
            "Bitget": self.get_bitget_depth(symbol),
            "BingX": self.get_bingx_depth(symbol),
            "Huobi": self.get_huobi_depth(symbol),
            "Gate": self.get_gate_depth(symbol),
        }

        # 计算千三滑点范围（0.3%）
        price_range = current_price * 0.003
        min_price = current_price - price_range
        max_price = current_price + price_range

        for exchange, depth_data in exchanges_data.items():
            if depth_data:
                try:
                    available_qty_usdt = self.calculate_available_quantity_in_range(
                        depth_data, min_price, max_price, exchange
                    )
                    results[exchange] = available_qty_usdt
                except Exception as e:
                    print(f"  {exchange}数据处理失败: {e}")
                    results[exchange] = 0
            else:
                results[exchange] = 0

        return results

    def calculate_available_quantity_in_range(self, depth_data, min_price, max_price, exchange):
        """计算指定价格范围内的可开数量(USDT)"""
        total_quantity_usdt = 0

        # 根据不同交易所的数据格式处理
        if exchange == "OKX":
            bids = depth_data.get("bids", [])
            asks = depth_data.get("asks", [])
        elif exchange == "Bybit":
            bids = depth_data.get("b", [])
            asks = depth_data.get("a", [])
        elif exchange == "Bitget":
            bids = depth_data.get("bids", [])
            asks = depth_data.get("asks", [])
        elif exchange == "BingX":
            bids = depth_data.get("bids", [])
            asks = depth_data.get("asks", [])
        elif exchange == "Huobi":
            bids = depth_data.get("bids", [])
            asks = depth_data.get("asks", [])
        elif exchange == "Gate":
            bids = depth_data.get("bids", [])
            asks = depth_data.get("asks", [])
        else:
            return 0

        # 计算买单量（在价格范围内，转换为USDT）
        for price_qty in bids:
            if len(price_qty) >= 2:
                price = float(price_qty[0])
                qty = float(price_qty[1])
                if min_price <= price <= max_price:
                    total_quantity_usdt += price * qty

        # 计算卖单量（在价格范围内，转换为USDT）
        for price_qty in asks:
            if len(price_qty) >= 2:
                price = float(price_qty[0])
                qty = float(price_qty[1])
                if min_price <= price <= max_price:
                    total_quantity_usdt += price * qty

        return total_quantity_usdt

    def process_kline_data(self, kline_data):
        """处理K线数据，提取收盘价"""
        if not kline_data:
            return None

        prices = []
        timestamps = []

        for kline in kline_data:
            timestamp = kline[0]  # 开盘时间
            close_price = float(kline[4])  # 收盘价

            prices.append(close_price)
            timestamps.append(timestamp)

        return pd.Series(prices, index=timestamps)

    def calculate_correlation(self, price_series1, price_series2):
        """
        计算两个价格序列的相关性

        注意：我们计算的是价格变化率（收益率）的相关性，而不是价格绝对值的相关性
        这种方法是正确的，因为：
        1. 价格变化率已经是标准化的：(price_t - price_t-1) / price_t-1
        2. 相关性本身就是标准化的度量，不受价格绝对值影响
        3. 这是金融学中分析资产相关性的标准做法
        """
        if price_series1 is None or price_series2 is None:
            return None

        if len(price_series1) == 0 or len(price_series2) == 0:
            return None

        # 确保两个序列长度相同
        min_length = min(len(price_series1), len(price_series2))
        if min_length < 100:  # 至少需要100个数据点
            return None

        series1 = price_series1.iloc[-min_length:]
        series2 = price_series2.iloc[-min_length:]

        # 计算价格变化率（收益率）
        # 这里自动进行了标准化：无论BTC价格是110,000还是其他币种价格是0.1
        # 变化率都是无量纲的比率
        returns1 = series1.pct_change().dropna()
        returns2 = series2.pct_change().dropna()

        if len(returns1) < 50 or len(returns2) < 50:
            return None

        # 过滤异常值（变化率超过50%的数据点）
        returns1 = returns1[(returns1.abs() <= 0.5)]
        returns2 = returns2[(returns2.abs() <= 0.5)]

        if len(returns1) < 50 or len(returns2) < 50:
            return None

        # 确保两个序列对应相同的时间点
        common_index = returns1.index.intersection(returns2.index)
        if len(common_index) < 50:
            return None

        returns1_aligned = returns1.loc[common_index]
        returns2_aligned = returns2.loc[common_index]

        # 计算皮尔逊相关系数
        correlation = returns1_aligned.corr(returns2_aligned)

        return correlation if not pd.isna(correlation) else None

    def get_correlation_description(self, correlation):
        """获取相关性描述"""
        if correlation is None:
            return "无数据"

        abs_corr = abs(correlation)
        if abs_corr >= 0.8:
            return "强相关"
        elif abs_corr >= 0.6:
            return "中等相关"
        elif abs_corr >= 0.3:
            return "弱相关"
        else:
            return "不相关"

    def check_price_volume_correlation(self, symbol, limit=1440):
        """
        检查价格波动与成交量变化的关联性
        如果价格发生波动，那么成交量也是随之变化，即价格波动越剧烈，成交量也越大
        反之如果价格波动，但是成交量没有明显变化的就过滤掉

        返回: (是否通过检查, 价格波动率, 成交量变化率, 相关性)
        """
        try:
            # 获取K线数据（包含价格和成交量）
            kline_data = self.get_kline_data(symbol, interval="1m", limit=limit)
            if not kline_data or len(kline_data) < 100:
                return False, 0, 0, None

            prices = []
            volumes = []

            for kline in kline_data:
                close_price = float(kline[4])  # 收盘价
                volume = float(kline[5])  # 成交量
                prices.append(close_price)
                volumes.append(volume)

            # 转换为pandas Series
            price_series = pd.Series(prices)
            volume_series = pd.Series(volumes)

            # 计算价格变化率（收益率）
            price_returns = price_series.pct_change().dropna()
            # 计算成交量变化率
            volume_returns = volume_series.pct_change().dropna()

            # 过滤异常值
            price_returns = price_returns[(price_returns.abs() <= 0.5)]
            volume_returns = volume_returns[(volume_returns.abs() <= 5.0)]  # 成交量变化可能更大

            if len(price_returns) < 50 or len(volume_returns) < 50:
                return False, 0, 0, None

            # 确保两个序列对应相同的时间点
            common_index = price_returns.index.intersection(volume_returns.index)
            if len(common_index) < 50:
                return False, 0, 0, None

            price_returns_aligned = price_returns.loc[common_index]
            volume_returns_aligned = volume_returns.loc[common_index]

            # 计算价格波动率（标准差）
            price_volatility = price_returns_aligned.std()
            # 计算成交量变化率（标准差）
            volume_volatility = volume_returns_aligned.std()

            # 计算价格变化与成交量变化的相关性
            correlation = price_returns_aligned.abs().corr(volume_returns_aligned.abs())

            # 判断标准：
            # 1. 价格波动率不能太小（至少要有一定波动）
            # 2. 成交量变化率不能太小（成交量要有变化）
            # 3. 价格波动与成交量变化要有正相关性

            price_vol_threshold = self.config["price_vol_threshold"]
            volume_vol_threshold = self.config["volume_vol_threshold"]
            correlation_threshold = self.config["price_volume_corr_threshold"]

            passes_check = (
                price_volatility >= price_vol_threshold
                and volume_volatility >= volume_vol_threshold
                and correlation is not None
                and correlation >= correlation_threshold
            )

            return passes_check, price_volatility, volume_volatility, correlation

        except Exception as e:
            print(f"  价格成交量关联性检查失败: {e}")
            return False, 0, 0, None

    def check_trade_balance(self, symbol):
        """
        检查近24小时的trade相对平衡
        即isBuyerMaker true 和 false比例相对均衡

        返回: (是否通过检查, 买方比例, 卖方比例, 平衡度)
        """
        try:
            # 获取最近24小时的交易数据
            trades_data = self.get_24h_trades(symbol)
            if not trades_data or len(trades_data) < 100:
                return False, 0, 0, 0

            # 统计买卖方向
            buyer_maker_count = 0  # isBuyerMaker = true (买方是挂单方，即卖单成交)
            seller_maker_count = 0  # isBuyerMaker = false (卖方是挂单方，即买单成交)

            buyer_maker_volume = 0
            seller_maker_volume = 0

            for trade in trades_data:
                is_buyer_maker = trade["m"]  # isBuyerMaker字段
                quantity = float(trade["q"])

                if is_buyer_maker:
                    buyer_maker_count += 1
                    buyer_maker_volume += quantity
                else:
                    seller_maker_count += 1
                    seller_maker_volume += quantity

            total_count = buyer_maker_count + seller_maker_count
            total_volume = buyer_maker_volume + seller_maker_volume

            if total_count == 0 or total_volume == 0:
                return False, 0, 0, 0

            # 计算比例
            buyer_ratio = buyer_maker_count / total_count
            seller_ratio = seller_maker_count / total_count

            buyer_volume_ratio = buyer_maker_volume / total_volume
            seller_volume_ratio = seller_maker_volume / total_volume

            # 计算平衡度 (越接近0.5越平衡，我们计算偏离0.5的程度)
            count_balance = 1 - abs(buyer_ratio - 0.5) * 2  # 转换为0-1之间，1表示完全平衡
            volume_balance = 1 - abs(buyer_volume_ratio - 0.5) * 2

            # 综合平衡度
            overall_balance = (count_balance + volume_balance) / 2

            # 判断标准：平衡度要大于配置的阈值
            balance_threshold = self.config["trade_balance_threshold"]
            passes_check = overall_balance >= balance_threshold

            return passes_check, buyer_ratio, seller_ratio, overall_balance

        except Exception as e:
            print(f"  交易平衡性检查失败: {e}")
            return False, 0, 0, 0

    def check_net_flow_price_consistency(self, symbol):
        """
        检查净流入时上涨，净流出时下跌的一致性
        计算方法：把净流入和净流出都按照1分钟聚合，然后和1分钟K线计算相关性

        返回: (是否通过检查, 平均净流入, 平均价格变化率, 相关性)
        """
        try:
            # 获取1分钟K线数据（24小时 = 1440分钟）
            kline_data = self.get_kline_data(symbol, interval="1m", limit=1440)
            if not kline_data or len(kline_data) < 100:
                return False, 0, 0, 0

            # 获取24小时交易数据
            trades_data = self.get_24h_trades(symbol)
            if not trades_data or len(trades_data) < 100:
                return False, 0, 0, 0

            # 按1分钟聚合净流入数据
            minute_net_flows = {}  # {timestamp: net_flow}

            for trade in trades_data:
                # 获取交易时间戳，转换为分钟级别
                trade_time = int(trade["T"])  # 交易时间戳（毫秒）
                minute_timestamp = (trade_time // 60000) * 60000  # 向下取整到分钟

                is_buyer_maker = trade["m"]  # isBuyerMaker字段
                price = float(trade["p"])
                quantity = float(trade["q"])
                volume_usdt = price * quantity

                if minute_timestamp not in minute_net_flows:
                    minute_net_flows[minute_timestamp] = 0

                if is_buyer_maker:
                    # 买方是挂单方，说明是卖单成交（主动卖出）
                    minute_net_flows[minute_timestamp] -= volume_usdt
                else:
                    # 卖方是挂单方，说明是买单成交（主动买入）
                    minute_net_flows[minute_timestamp] += volume_usdt

            # 处理K线数据，计算每分钟的价格变化率
            minute_price_changes = {}  # {timestamp: price_change_pct}

            for i, kline in enumerate(kline_data):
                if i == 0:
                    continue  # 跳过第一根K线，因为无法计算变化率

                current_close = float(kline[4])
                previous_close = float(kline_data[i - 1][4])

                if previous_close > 0:
                    price_change_pct = (current_close - previous_close) / previous_close
                    kline_timestamp = int(kline[0])  # K线开始时间戳
                    minute_price_changes[kline_timestamp] = price_change_pct

            # 找到时间戳匹配的数据点
            matched_net_flows = []
            matched_price_changes = []

            for timestamp in minute_net_flows:
                if timestamp in minute_price_changes:
                    matched_net_flows.append(minute_net_flows[timestamp])
                    matched_price_changes.append(minute_price_changes[timestamp])

            if len(matched_net_flows) < 50:  # 至少需要50个数据点
                return False, 0, 0, 0

            # 转换为pandas Series计算相关性
            import pandas as pd

            net_flow_series = pd.Series(matched_net_flows)
            price_change_series = pd.Series(matched_price_changes)

            # 数据标准化（Z-score标准化）
            # 这样可以消除量级差异，更准确地计算相关性
            net_flow_normalized = (net_flow_series - net_flow_series.mean()) / net_flow_series.std()
            price_change_normalized = (price_change_series - price_change_series.mean()) / price_change_series.std()

            # 计算标准化后的相关性
            correlation = net_flow_normalized.corr(price_change_normalized)

            # 也计算原始数据的相关性用于对比
            correlation_raw = net_flow_series.corr(price_change_series)

            # 计算平均值用于显示
            avg_net_flow = net_flow_series.mean()
            avg_price_change = price_change_series.mean()

            # 判断标准：相关性 > 0.2 表示净流入与价格变化有正相关关系
            correlation_threshold = 0.2
            passes_check = correlation is not None and correlation >= correlation_threshold

            return passes_check, avg_net_flow, avg_price_change, correlation

        except Exception as e:
            print(f"  净流入价格一致性检查失败: {e}")
            return False, 0, 0, 0

    def check_multi_exchange_availability(self, symbol):
        """
        检查币种是否在主要交易所有期货交易
        要求：okx，bybit，bitget，bingx，huobi，Gate, binance 中至少5个交易所可用

        返回: (是否通过检查, 可用交易所列表, 不可用交易所列表)
        """
        try:
            available_exchanges = []
            unavailable_exchanges = []

            # Binance 已经确认有（因为我们从Binance获取的数据）
            available_exchanges.append("Binance")

            # 检查其他交易所
            exchanges_to_check = {
                "OKX": self.get_okx_depth,
                "Bybit": self.get_bybit_depth,
                "Bitget": self.get_bitget_depth,
                "BingX": self.get_bingx_depth,
                "Huobi": self.get_huobi_depth,
                "Gate": self.get_gate_depth,
            }

            for exchange_name, get_depth_func in exchanges_to_check.items():
                try:
                    depth_data = get_depth_func(symbol, limit=5)  # 只获取少量数据来测试可用性
                    if depth_data and self.validate_depth_data(depth_data, exchange_name):
                        available_exchanges.append(exchange_name)
                    else:
                        unavailable_exchanges.append(exchange_name)
                except Exception:
                    unavailable_exchanges.append(exchange_name)

            # 要求7个交易所中至少5个可用
            min_required_exchanges = 5
            passes_check = len(available_exchanges) >= min_required_exchanges

            return passes_check, available_exchanges, unavailable_exchanges

        except Exception as e:
            print(f"  多交易所可用性检查失败: {e}")
            return False, ["Binance"], ["OKX", "Bybit", "Bitget", "BingX", "Huobi", "Gate"]

    def validate_depth_data(self, depth_data, exchange):
        """验证深度数据是否有效"""
        try:
            if exchange == "OKX":
                return "bids" in depth_data and "asks" in depth_data and len(depth_data["bids"]) > 0
            elif exchange == "Bybit":
                return "b" in depth_data and "a" in depth_data and len(depth_data["b"]) > 0
            elif exchange in ["Bitget", "BingX", "Huobi", "Gate"]:
                return "bids" in depth_data and "asks" in depth_data and len(depth_data["bids"]) > 0
            return False
        except Exception:
            return False

    def batch_check_exchange_availability(self, symbols):
        """
        批量检查多个币种在各交易所的可用性
        返回: {symbol: {'available': [exchanges], 'unavailable': [exchanges]}}
        """
        print(f"批量检查 {len(symbols)} 个币种的多交易所可用性...")

        # 尝试加载缓存
        cache_data = self.load_cache()

        results = {}
        symbols_to_check = []

        # 检查哪些币种需要重新检查
        for symbol in symbols:
            if symbol in cache_data:
                results[symbol] = cache_data[symbol]
                print(f"  ✓ {symbol} (来自缓存)")
            else:
                symbols_to_check.append(symbol)

        if symbols_to_check:
            print(f"需要检查 {len(symbols_to_check)} 个新币种...")

            # 检查新币种
            for i, symbol in enumerate(symbols_to_check):
                print(f"检查 {symbol} 多交易所可用性 ({i+1}/{len(symbols_to_check)})")

                passes_check, available, unavailable = self.check_multi_exchange_availability(symbol)

                results[symbol] = {"passes_check": passes_check, "available": available, "unavailable": unavailable}

                if passes_check:
                    print(f"  ✓ 通过检查 - 可用交易所: {len(available)}/7")
                else:
                    print(f"  ✗ 未通过检查 - 可用: {available}, 不可用: {unavailable}")

            # 更新缓存（合并新旧数据）
            updated_cache = cache_data.copy()
            for symbol in symbols_to_check:
                updated_cache[symbol] = results[symbol]

            self.save_cache(updated_cache)

        return results

    def analyze_combined(self):
        """综合分析"""
        print("=== 第一步：获取现货和期货基础数据 ===")

        # 获取Binance交易对信息
        common_symbols, spot_symbols, futures_symbols = self.get_binance_symbols()

        if not common_symbols:
            print("获取基础数据失败")
            return []

        # 获取24小时数据
        spot_24hr = self.get_spot_24hr_ticker()
        futures_24hr = self.get_futures_24hr_ticker()

        if not all([spot_24hr, futures_24hr]):
            print("获取24小时数据失败")
            return []

        print("基础数据获取完成")

        # 构建24小时数据字典
        spot_24hr_data = {}
        futures_24hr_data = {}

        # 处理24小时数据
        for ticker in spot_24hr:
            spot_24hr_data[ticker["symbol"]] = {
                "last_price": float(ticker["lastPrice"]),
                "volume": float(ticker["volume"]),
                "quote_volume": float(ticker["quoteVolume"]),
            }

        for ticker in futures_24hr:
            futures_24hr_data[ticker["symbol"]] = {
                "last_price": float(ticker["lastPrice"]),
                "volume": float(ticker["volume"]),
                "quote_volume": float(ticker["quoteVolume"]),
            }

        if self.config["enable_multi_exchange_check"]:
            print("\n=== 第二步：检查多交易所可用性 ===")
            print("检查币种是否在主要交易所有期货交易...")
            print("要求：okx，bybit，bitget，bingx，huobi，Gate, binance 中至少4个交易所可用")

            # 使用批量检查方法
            exchange_results = self.batch_check_exchange_availability(list(common_symbols))

            multi_exchange_symbols = []
            exchange_stats = {}

            for symbol, result in exchange_results.items():
                available_count = len(result["available"])
                if available_count >= self.config["min_exchange_count"]:  # 使用配置的最小交易所数量
                    multi_exchange_symbols.append(symbol)

                # 统计各交易所数量分布
                if available_count not in exchange_stats:
                    exchange_stats[available_count] = 0
                exchange_stats[available_count] += 1

            print(f"\n交易所可用性统计:")
            for count in sorted(exchange_stats.keys(), reverse=True):
                print(f"  {count}个交易所可用: {exchange_stats[count]} 个币种")

            print(
                f"\n在至少{self.config['min_exchange_count']}个主要交易所可用的币种数量: {len(multi_exchange_symbols)}"
            )
            common_symbols = set(multi_exchange_symbols)
        else:
            print("\n=== 第二步：跳过多交易所可用性检查 ===")

        print("\n=== 第三步：应用基础筛选条件 ===")
        print("筛选条件：")
        print(f"1. 现货24小时交易量 < {self.config['spot_volume_limit']:,} USDT")
        print(f"2. 期货交易量 >= 现货交易量的{self.config['futures_volume_ratio']*100:.0f}%")
        if self.config["enable_spot_other_futures_check"]:
            print(f"3. 现货与非Binance期货交易量比值 < {self.config['spot_to_other_futures_ratio']*100:.0f}%")

        basic_results = []
        filtered_count = 0

        for symbol in common_symbols:
            if symbol in spot_24hr_data and symbol in futures_24hr_data:
                spot_data = spot_symbols[symbol]
                spot_ticker = spot_24hr_data[symbol]
                futures_ticker = futures_24hr_data[symbol]

                # 应用筛选条件
                spot_volume = spot_ticker["quote_volume"]
                futures_volume = futures_ticker["quote_volume"]

                # 条件1：现货24小时交易量限制
                if spot_volume >= self.config["spot_volume_limit"]:
                    continue

                # 条件2：期货交易量比例要求
                if futures_volume < spot_volume * self.config["futures_volume_ratio"]:
                    continue

                # 条件3：现货与非Binance期货交易量比值检查
                other_futures_volumes = {}
                spot_to_other_futures_ratio = 0
                total_other_futures_volume = 0

                if self.config["enable_spot_other_futures_check"]:
                    # 获取其他交易所期货交易量
                    other_futures_volumes = self.get_all_exchanges_24hr_volume(symbol)
                    total_other_futures_volume = sum(other_futures_volumes.values())

                    if total_other_futures_volume > 0:
                        spot_to_other_futures_ratio = spot_volume / total_other_futures_volume
                        # 如果比值大于等于阈值，则跳过
                        if spot_to_other_futures_ratio >= self.config["spot_to_other_futures_ratio"]:
                            print(
                                f"  跳过 {symbol}: 现货与非Binance期货比值过高 ({spot_to_other_futures_ratio:.3f} >= {self.config['spot_to_other_futures_ratio']:.3f})"
                            )
                            continue
                    else:
                        print(f"  跳过 {symbol}: 无法获取其他交易所期货交易量数据")
                        continue

                filtered_count += 1

                # 计算交易量比值
                volume_ratio = futures_volume / spot_volume if spot_volume > 0 else 0

                result_data = {
                    "symbol": symbol,
                    "base_asset": spot_data["base_asset"],
                    "spot_tick_size": spot_data["tick_size"],
                    "spot_last_price": spot_ticker["last_price"],
                    "spot_volume_24h": spot_volume,
                    "futures_volume_24h": futures_volume,
                    "volume_ratio": volume_ratio,
                    "spot_to_other_futures_ratio": spot_to_other_futures_ratio,
                    "total_other_futures_volume": total_other_futures_volume,
                }

                # 添加其他交易所期货交易量数据
                for exchange, volume in other_futures_volumes.items():
                    result_data[f"{exchange.lower()}_futures_volume"] = volume

                basic_results.append(result_data)

        print(f"符合筛选条件的币种数量: {filtered_count}")

        # 直接使用基础筛选结果，不再按tick ratio排序
        print(f"进入下一步分析的币种数量: {len(basic_results)}")

        # 直接使用基础筛选结果进行后续分析
        current_results = basic_results

        print("\n=== 第四步：跳过价格波动与成交量关联性检查 ===")
        # 根据新的filter_rule.txt，不需要价格成交量关联性检查
        volume_corr_results = current_results

        if self.config["enable_trade_balance_check"]:
            print("\n=== 第五步：检查交易平衡性 ===")

            # 筛选交易平衡的币种
            trade_balance_results = []
            for i, item in enumerate(volume_corr_results):
                symbol = item["symbol"]
                print(f"检查交易平衡性 {symbol} ({i+1}/{len(volume_corr_results)})")

                passes_check, buyer_ratio, seller_ratio, balance_score = self.check_trade_balance(symbol)

                if passes_check:
                    result = item.copy()
                    result.update(
                        {"buyer_ratio": buyer_ratio, "seller_ratio": seller_ratio, "trade_balance_score": balance_score}
                    )
                    trade_balance_results.append(result)
                    print(
                        f"  ✓ 通过检查 - 买方比例: {buyer_ratio:.3f}, 卖方比例: {seller_ratio:.3f}, 平衡度: {balance_score:.3f}"
                    )
                else:
                    print(
                        f"  ✗ 未通过检查 - 买方比例: {buyer_ratio:.3f}, 卖方比例: {seller_ratio:.3f}, 平衡度: {balance_score:.3f}"
                    )

            print(f"\n交易平衡的币种数量: {len(trade_balance_results)}")
        else:
            print("\n=== 第五步：跳过交易平衡性检查 ===")
            trade_balance_results = volume_corr_results

        if self.config["enable_net_flow_check"]:
            print("\n=== 第六步：检查净流入与价格变化一致性 ===")

            # 筛选净流入与价格变化一致的币种
            net_flow_results = []
            for i, item in enumerate(trade_balance_results):
                symbol = item["symbol"]
                print(f"检查净流入价格一致性 {symbol} ({i+1}/{len(trade_balance_results)})")

                passes_check, net_inflow, price_change, consistency_score = self.check_net_flow_price_consistency(
                    symbol
                )

                if passes_check:
                    result = item.copy()
                    result.update(
                        {
                            "avg_net_inflow": net_inflow,
                            "avg_price_change_pct": price_change,
                            "flow_price_correlation": consistency_score,
                        }
                    )
                    net_flow_results.append(result)
                    flow_direction = "净流入" if net_inflow > 0 else "净流出" if net_inflow < 0 else "平衡"
                    price_direction = "上涨" if price_change > 0 else "下跌" if price_change < 0 else "平盘"
                    print(
                        f"  ✓ 通过检查 - 平均{flow_direction}: {net_inflow:.0f} USDT/分钟, 平均价格{price_direction}: {price_change:.6f}, 相关性: {consistency_score:.3f}"
                    )
                else:
                    flow_direction = "净流入" if net_inflow > 0 else "净流出" if net_inflow < 0 else "平衡"
                    price_direction = "上涨" if price_change > 0 else "下跌" if price_change < 0 else "平盘"
                    print(
                        f"  ✗ 未通过检查 - 平均{flow_direction}: {net_inflow:.0f} USDT/分钟, 平均价格{price_direction}: {price_change:.6f}, 相关性: {consistency_score:.3f}"
                    )

            print(f"\n净流入与价格变化一致的币种数量: {len(net_flow_results)}")
        else:
            print("\n=== 第六步：跳过净流入价格一致性检查 ===")
            net_flow_results = trade_balance_results

        print("\n=== 第七步：分析深度数据和挂单量 ===")

        final_filtered_results = []
        for i, item in enumerate(net_flow_results):
            symbol = item["symbol"]
            print(f"分析深度数据 {symbol} ({i+1}/{len(net_flow_results)})")

            # 获取现货和期货深度数据
            spot_depth = self.get_spot_depth(symbol)
            futures_depth = self.get_futures_depth(symbol)

            if spot_depth and futures_depth:
                # 计算挂单量（USDT金额）
                spot_bid_qty_usdt, spot_ask_qty_usdt = self.calculate_depth_quantity(spot_depth, item["spot_tick_size"])
                futures_bid_qty_usdt, futures_ask_qty_usdt = self.calculate_depth_quantity(
                    futures_depth, futures_symbols[symbol]["tick_size"]
                )

                # 计算期货/现货挂单量比值（USDT金额比值）
                total_spot_qty_usdt = spot_bid_qty_usdt + spot_ask_qty_usdt
                total_futures_qty_usdt = futures_bid_qty_usdt + futures_ask_qty_usdt

                depth_ratio = total_futures_qty_usdt / total_spot_qty_usdt if total_spot_qty_usdt > 0 else 0

                # 条件5：期货的千三滑点挂单量比例要求
                if depth_ratio >= self.config["depth_ratio_threshold"]:
                    # 获取其他交易所的千三滑点可开数量
                    current_price = item["spot_last_price"]
                    print(f"  获取其他交易所 {symbol} 千三滑点数据...")
                    other_exchanges_qty = self.get_all_exchanges_depth(symbol, current_price)

                    futures_tick_size = futures_symbols[symbol]["tick_size"]
                    spot_tick_size = item["spot_tick_size"]

                    result = item.copy()
                    result.update(
                        {
                            "spot_depth_qty_usdt": total_spot_qty_usdt,
                            "futures_depth_qty_usdt": total_futures_qty_usdt,
                            "depth_ratio": depth_ratio,
                            "futures_tick_size": futures_tick_size,
                            "tick_size_diff": futures_tick_size != spot_tick_size,
                            "current_price": current_price,
                            "okx_qty_usdt": other_exchanges_qty.get("OKX", 0),
                            "bybit_qty_usdt": other_exchanges_qty.get("Bybit", 0),
                            "bitget_qty_usdt": other_exchanges_qty.get("Bitget", 0),
                            "bingx_qty_usdt": other_exchanges_qty.get("BingX", 0),
                            "huobi_qty_usdt": other_exchanges_qty.get("Huobi", 0),
                            "gate_qty_usdt": other_exchanges_qty.get("Gate", 0),
                        }
                    )
                    final_filtered_results.append(result)
                else:
                    print(f"  跳过 {symbol}: 期货挂单量比值不足2倍 ({depth_ratio:.2f})")
            else:
                print(f"  跳过 {symbol}: 深度数据获取失败")

        print(f"\n最终符合所有条件的币种数量: {len(final_filtered_results)}")

        return basic_results, final_filtered_results

    def display_results(self, all_results, final_results):
        """显示分析结果"""
        print("\n" + "=" * 150)
        print("综合分析结果：符合基础筛选条件的币种")
        spot_limit_str = (
            f"{self.config['spot_volume_limit']/1_000_000:.0f}M"
            if self.config["spot_volume_limit"] >= 1_000_000
            else f"{self.config['spot_volume_limit']:,}"
        )
        print(
            f"基础条件：现货24h交易量<{spot_limit_str} USDT 且 期货交易量>=现货{self.config['futures_volume_ratio']*100:.0f}% 且 现货与非Binance期货比值<{self.config['spot_to_other_futures_ratio']*100:.0f}%"
        )
        print("=" * 150)
        print(
            f"{'排名':<4} {'交易对':<15} {'基础币种':<10} {'现货价格':<12} "
            f"{'现货交易量':<15} {'期货交易量':<15} {'交易量比值':<12} {'现货/其他期货比值':<20}"
        )
        print("=" * 150)

        for i, item in enumerate(all_results, 1):
            spot_volume_str = self.format_volume(item["spot_volume_24h"])
            futures_volume_str = self.format_volume(item["futures_volume_24h"])
            spot_to_other_ratio = item.get("spot_to_other_futures_ratio", 0)
            print(
                f"{i:<4} {item['symbol']:<15} {item['base_asset']:<10} "
                f"{item['spot_last_price']:<12.3f} "
                f"{spot_volume_str:<15} {futures_volume_str:<15} {item['volume_ratio']:<12.1f} "
                f"{spot_to_other_ratio:<20.3f}"
            )

        if final_results:
            print("\n" + "=" * 80)
            print("🎯 最终筛选结果：符合所有filter_rule.txt条件的币种")
            print("=" * 80)

            for i, item in enumerate(final_results, 1):
                print(f"\n📊 币种 {i}: {item['symbol']}")
                print("-" * 50)

                # 基础信息
                print(f"💰 交易量信息:")
                print(f"   现货交易量: {self.format_volume(item['spot_volume_24h'])}")
                print(f"   期货交易量: {self.format_volume(item['futures_volume_24h'])}")
                print(f"   交易量比值: {item['volume_ratio']:.1f}倍")
                print(f"   现货/其他期货比值: {item.get('spot_to_other_futures_ratio', 0):.3f}")

                # 深度信息
                print(f"📊 深度信息:")
                print(f"   现货十档挂单量: {self.format_volume(item['spot_depth_qty_usdt'])}")
                print(f"   期货十档挂单量: {self.format_volume(item['futures_depth_qty_usdt'])}")
                print(f"   挂单比值: {item['depth_ratio']:.2f}倍")

                # 其他交易所信息
                print(f"🏢 其他交易所千三滑点可开数量:")
                exchanges = [
                    ("OKX", item["okx_qty_usdt"]),
                    ("Bybit", item["bybit_qty_usdt"]),
                    ("Bitget", item["bitget_qty_usdt"]),
                    ("BingX", item["bingx_qty_usdt"]),
                    ("Huobi", item["huobi_qty_usdt"]),
                    ("Gate", item["gate_qty_usdt"]),
                ]

                for exchange, qty in exchanges:
                    if qty > 0:
                        print(f"   {exchange}: {self.format_volume(qty)}")
                    else:
                        print(f"   {exchange}: N/A")

            print(f"\n✅ 总计找到 {len(final_results)} 个符合所有filter_rule.txt条件的币种")

            # 显示Tick Size详细信息
            print("\n" + "=" * 120)
            print("📊 Tick Size详细信息（用于验证期货现货tick size差异）")
            print("=" * 120)
            print(f"{'交易对':<15} {'现货Tick Size':<20} {'期货Tick Size':<20} {'是否需要换算':<15} {'当前价格':<15}")
            print("=" * 120)

            for item in final_results:
                need_convert = "是" if item["tick_size_diff"] else "否"
                print(
                    f"{item['symbol']:<15} {item['spot_tick_size']:<20.8f} "
                    f"{item['futures_tick_size']:<20.8f} {need_convert:<15} {item['current_price']:<15.4f}"
                )
        else:
            print("\n❌ 没有找到同时满足所有filter_rule.txt条件的币种")

    def save_to_csv(self, all_results, final_results):
        """保存结果到CSV文件"""
        if all_results:
            df_all = pd.DataFrame(all_results)
            df_all.to_csv("basic_filtered_coins.csv", index=False, encoding="utf-8-sig")
            print(f"\n基础筛选结果已保存到 basic_filtered_coins.csv")

        if final_results:
            df_final = pd.DataFrame(final_results)
            df_final.to_csv("final_filtered_coins.csv", index=False, encoding="utf-8-sig")
            print(f"最终筛选结果已保存到 final_filtered_coins.csv")

            # 按照filter_rule.txt表头格式保存数据
            formatted_data = []
            for item in final_results:
                formatted_data.append(
                    {
                        "交易对": item["symbol"],
                        "现货交易量": item["spot_volume_24h"],
                        "期货交易量": item["futures_volume_24h"],
                        "交易量比值": item["volume_ratio"],
                        "现货/其他期货比值": item.get("spot_to_other_futures_ratio", 0),
                        "现货十档挂单量(USDT)": item["spot_depth_qty_usdt"],
                        "期货十档挂单量(USDT)": item["futures_depth_qty_usdt"],
                        "挂单比值": item["depth_ratio"],
                        "OKX千三滑点可开数量(USDT)": item["okx_qty_usdt"],
                        "Bybit千三滑点可开数量(USDT)": item["bybit_qty_usdt"],
                        "Bitget千三滑点可开数量(USDT)": item["bitget_qty_usdt"],
                        "BingX千三滑点可开数量(USDT)": item["bingx_qty_usdt"],
                        "Huobi千三滑点可开数量(USDT)": item["huobi_qty_usdt"],
                        "Gate千三滑点可开数量(USDT)": item["gate_qty_usdt"],
                    }
                )

            if formatted_data:
                df_formatted = pd.DataFrame(formatted_data)
                df_formatted.to_csv("filter_rule_results.csv", index=False, encoding="utf-8-sig")
                print(f"按filter_rule.txt格式的结果已保存到 filter_rule_results.csv")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="币种筛选分析工具 - 根据filter_rule.txt规则筛选符合条件的币种",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 使用默认参数
  python combined_analysis.py

  # 自定义现货交易量限制和tick占比
  python combined_analysis.py --spot-volume 10000000 --tick-ratio 0.0005

  # 禁用价格成交量关联检查
  python combined_analysis.py --no-price-volume-check

  # 禁用交易平衡检查
  python combined_analysis.py --no-trade-balance-check

  # 调整所有主要参数
  python combined_analysis.py --spot-volume 5000000 --futures-ratio 6.0 --tick-ratio 0.0002 --btc-corr 0.3
        """,
    )

    # 基础筛选参数
    parser.add_argument("--spot-volume", type=float, default=8_000_000, help="现货交易量限制 (USDT) (默认: 8,000,000)")
    parser.add_argument("--futures-ratio", type=float, default=4.0, help="期货/现货交易量比例要求 (默认: 4.0)")
    parser.add_argument("--depth-ratio", type=float, default=2.0, help="期货/现货挂单量比例阈值 (默认: 2.0)")
    parser.add_argument("--min-exchanges", type=int, default=7, help="最小交易所数量要求 (默认: 7)")
    parser.add_argument(
        "--spot-other-futures-ratio", type=float, default=0.2, help="现货与非Binance期货交易量比值阈值 (默认: 0.2)"
    )

    # 交易平衡检查参数
    parser.add_argument("--trade-balance-threshold", type=float, default=0.6, help="交易平衡度阈值 (默认: 0.6)")

    # 功能开关
    parser.add_argument("--no-multi-exchange-check", action="store_true", help="禁用多交易所可用性检查")
    parser.add_argument("--no-trade-balance-check", action="store_true", help="禁用交易平衡性检查")
    parser.add_argument("--no-net-flow-check", action="store_true", help="禁用净流入与价格变化一致性检查")
    parser.add_argument("--no-spot-other-futures-check", action="store_true", help="禁用现货与非Binance期货比值检查")

    return parser.parse_args()


def main():
    args = parse_arguments()

    # 构建配置
    config = {
        "spot_volume_limit": args.spot_volume,
        "futures_volume_ratio": args.futures_ratio,
        "depth_ratio_threshold": args.depth_ratio,
        "spot_to_other_futures_ratio": args.spot_other_futures_ratio,
        "trade_balance_threshold": args.trade_balance_threshold,
        "enable_multi_exchange_check": not args.no_multi_exchange_check,
        "enable_trade_balance_check": not args.no_trade_balance_check,
        "enable_net_flow_check": not args.no_net_flow_check,
        "enable_spot_other_futures_check": not args.no_spot_other_futures_check,
        "min_exchange_count": args.min_exchanges,
    }

    analyzer = CombinedAnalyzer(config)

    try:
        print("开始执行filter_rule.txt规则筛选...")
        print("当前配置参数：")

        if config["enable_multi_exchange_check"]:
            print(f"0. 多交易所可用性检查: 启用")
            print(
                f"   - 要求: okx，bybit，bitget，bingx，huobi，Gate, binance 中至少{config['min_exchange_count']}个交易所可用"
            )
        else:
            print(f"0. 多交易所可用性检查: 禁用")

        print(f"1. 现货交易量限制: {config['spot_volume_limit']:,} USDT")
        print(f"2. 期货/现货交易量比例: {config['futures_volume_ratio']*100:.0f}%")
        print(f"3. 期货/现货挂单量比例阈值: {config['depth_ratio_threshold']}")

        if config["enable_spot_other_futures_check"]:
            print(f"4. 现货与非Binance期货交易量比值检查: 启用")
            print(f"   - 比值阈值: {config['spot_to_other_futures_ratio']*100:.0f}%")
        else:
            print(f"4. 现货与非Binance期货交易量比值检查: 禁用")

        if config["enable_trade_balance_check"]:
            print(f"5. 交易平衡性检查: 启用")
            print(f"   - 平衡度阈值: {config['trade_balance_threshold']}")
        else:
            print(f"5. 交易平衡性检查: 禁用")

        if config["enable_net_flow_check"]:
            print(f"6. 净流入与价格变化一致性检查: 启用")
            print(f"   - 按1分钟聚合计算相关性，阈值: 0.2")
        else:
            print(f"6. 净流入与价格变化一致性检查: 禁用")

        print(f"7. 其他交易所千三滑点数据获取: 启用")
        print("\n表格格式：交易对，现货交易量，期货交易量，交易量比值，")
        print("现货十档挂单量（USDT），期货十档挂单量（USDT），挂单比值，")
        print("各其他交易所千三滑点可开数量(USDT)")

        all_results, final_results = analyzer.analyze_combined()

        if all_results:
            analyzer.display_results(all_results, final_results)
            analyzer.save_to_csv(all_results, final_results)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
