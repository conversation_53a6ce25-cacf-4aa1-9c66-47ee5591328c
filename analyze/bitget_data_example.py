#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bitget历史数据获取示例
演示如何使用BitgetHistoricalDataFetcher获取数据
"""

import os
import sys
from datetime import datetime
from bitget_historical_data_fetcher import BitgetHistoricalDataFetcher


def example_basic_usage():
    """基础使用示例"""
    print("=== Bitget历史数据获取基础示例 ===")
    
    # 创建客户端
    client = BitgetHistoricalDataFetcher()
    
    # 测试交易对
    symbol = "BTCUSDT"
    
    print(f"1. 验证交易对 {symbol}")
    if client.validate_symbol(symbol):
        print(f"✓ 交易对 {symbol} 有效")
    else:
        print(f"✗ 交易对 {symbol} 无效")
        return
    
    print(f"\n2. 获取 {symbol} 的orderbook快照")
    orderbook = client.get_orderbook_snapshot(symbol, limit=10)
    if orderbook:
        print(f"✓ 获取成功")
        print(f"  买盘档数: {len(orderbook['bids'])}")
        print(f"  卖盘档数: {len(orderbook['asks'])}")
        if orderbook['bids']:
            print(f"  最高买价: {orderbook['bids'][0][0]}")
        if orderbook['asks']:
            print(f"  最低卖价: {orderbook['asks'][0][0]}")
    else:
        print("✗ 获取失败")
    
    print(f"\n3. 获取 {symbol} 的最近交易数据")
    trades = client.get_recent_trades(symbol, limit=10)
    if trades:
        print(f"✓ 获取到 {len(trades)} 条交易记录")
        if trades:
            latest_trade = trades[0]
            print(f"  最新交易: 价格={latest_trade['price']}, 数量={latest_trade['size']}, 方向={latest_trade['side']}")
    else:
        print("✗ 获取失败")
    
    print(f"\n4. 获取 {symbol} 的K线数据")
    klines = client.get_kline_data(symbol, granularity='1m', limit=10)
    if klines:
        print(f"✓ 获取到 {len(klines)} 条K线记录")
        if klines:
            latest_kline = klines[0]
            print(f"  最新K线: 开={latest_kline['open']}, 高={latest_kline['high']}, 低={latest_kline['low']}, 收={latest_kline['close']}")
    else:
        print("✗ 获取失败")


def example_collect_historical_data():
    """收集历史数据示例"""
    print("\n=== 收集历史数据示例 ===")
    
    # 创建客户端
    client = BitgetHistoricalDataFetcher()
    
    # 测试交易对
    symbol = "ETHUSDT"
    hours = 1  # 获取过去1小时的数据
    
    print(f"收集 {symbol} 过去 {hours} 小时的历史数据...")
    
    try:
        # 收集数据
        data = client.collect_historical_data(symbol, hours=hours)
        
        print(f"✓ 数据收集完成")
        print(f"  K线数据: {len(data['klines'])} 条")
        print(f"  交易数据: {len(data['trades'])} 条")
        print(f"  Orderbook快照: {len(data['orderbooks'])} 个")
        
        # 保存到文件
        output_dir = "bitget_data_output"
        client.save_to_csv(data, output_dir)
        print(f"✓ 数据已保存到 {output_dir} 目录")
        
    except Exception as e:
        print(f"✗ 收集数据失败: {e}")


def example_multiple_symbols():
    """多个交易对示例"""
    print("\n=== 多个交易对数据获取示例 ===")
    
    # 创建客户端
    client = BitgetHistoricalDataFetcher()
    
    # 测试多个交易对
    symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
    
    for symbol in symbols:
        print(f"\n处理交易对: {symbol}")
        
        # 验证交易对
        if not client.validate_symbol(symbol):
            print(f"  ✗ 交易对 {symbol} 无效，跳过")
            continue
        
        # 获取orderbook
        orderbook = client.get_orderbook_snapshot(symbol, limit=5)
        if orderbook:
            spread = float(orderbook['asks'][0][0]) - float(orderbook['bids'][0][0]) if orderbook['asks'] and orderbook['bids'] else 0
            print(f"  ✓ Orderbook: 买={orderbook['bids'][0][0]}, 卖={orderbook['asks'][0][0]}, 价差={spread:.6f}")
        
        # 获取最近交易
        trades = client.get_recent_trades(symbol, limit=5)
        if trades:
            avg_price = sum(trade['price'] for trade in trades) / len(trades)
            print(f"  ✓ 最近交易: {len(trades)} 条, 平均价格={avg_price:.6f}")


def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    # 创建客户端
    client = BitgetHistoricalDataFetcher()
    
    symbol = "BTCUSDT"
    
    # 获取K线数据进行简单分析
    klines = client.get_kline_data(symbol, granularity='5m', limit=20)
    if klines:
        print(f"分析 {symbol} 最近20个5分钟K线:")
        
        prices = [kline['close'] for kline in klines]
        volumes = [kline['volume'] for kline in klines]
        
        # 计算简单统计
        avg_price = sum(prices) / len(prices)
        max_price = max(prices)
        min_price = min(prices)
        total_volume = sum(volumes)
        
        print(f"  平均价格: {avg_price:.2f}")
        print(f"  最高价格: {max_price:.2f}")
        print(f"  最低价格: {min_price:.2f}")
        print(f"  价格波动: {((max_price - min_price) / avg_price * 100):.2f}%")
        print(f"  总成交量: {total_volume:.2f}")
        
        # 计算价格趋势
        if len(prices) >= 2:
            price_change = prices[0] - prices[-1]  # 最新价格 - 最早价格
            trend = "上涨" if price_change > 0 else "下跌" if price_change < 0 else "横盘"
            print(f"  价格趋势: {trend} ({price_change:+.2f})")


def main():
    """主函数"""
    print("Bitget历史数据获取工具示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_collect_historical_data()
        example_multiple_symbols()
        example_data_analysis()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
