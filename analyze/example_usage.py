#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例脚本
演示如何使用Binance数据拉取和分析功能
"""

import os
import sys
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from binance_data_fetcher import BinanceDataFetcher

def main():
    """主函数 - 演示完整的数据拉取和分析流程"""

    print("=== Binance交易数据与K线关系分析示例 ===\n")

    # 1. 创建数据拉取器
    fetcher = BinanceDataFetcher()

    # 2. 拉取数据
    print("正在拉取BTCUSDT过去2小时的数据...")
    symbol = "BTCUSDT"
    hours = 2
    interval = "1m"

    trades_df, klines_df = fetcher.fetch_and_save_data(
        symbol=symbol,
        hours=hours,
        kline_interval=interval
    )

    if trades_df.empty or klines_df.empty:
        print("数据拉取失败，请检查网络连接")
        return

    print(f"数据拉取完成!")
    print(f"交易数据: {len(trades_df)} 条记录")
    print(f"K线数据: {len(klines_df)} 条记录")

    # 3. 基础数据分析
    print("\n=== 基础数据分析 ===")

    # 交易数据统计
    print(f"交易价格范围: {trades_df['price'].min():.2f} - {trades_df['price'].max():.2f}")
    print(f"平均交易价格: {trades_df['price'].mean():.2f}")
    print(f"总交易量: {trades_df['qty'].sum():.4f}")
    print(f"总交易金额: {trades_df['quoteQty'].sum():.2f}")

    # K线数据统计
    print(f"K线价格范围: {klines_df['low'].min():.2f} - {klines_df['high'].max():.2f}")
    print(f"平均成交量: {klines_df['volume'].mean():.4f}")
    print(f"总成交量: {klines_df['volume'].sum():.4f}")

    # 4. 关联分析
    print("\n=== 关联分析 ===")

    # 分析每个K线周期内的交易数据
    enriched_klines = analyze_trades_in_klines(trades_df, klines_df)

    # 计算相关性
    correlation_data = enriched_klines[['volume', 'total_volume', 'trade_count']].corr()

    print("相关性矩阵:")
    print(correlation_data)

    # 5. 可视化分析
    print("\n=== 生成可视化图表 ===")

    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 价格时间序列
    axes[0, 0].plot(trades_df['time'], trades_df['price'], alpha=0.7, linewidth=0.5)
    axes[0, 0].set_title(f'{symbol} 交易价格时间序列')
    axes[0, 0].set_ylabel('价格')
    axes[0, 0].grid(True, alpha=0.3)

    # K线收盘价
    axes[0, 1].plot(klines_df['open_time'], klines_df['close'], alpha=0.8, linewidth=1, color='green')
    axes[0, 1].set_title(f'{symbol} K线收盘价时间序列')
    axes[0, 1].set_ylabel('收盘价')
    axes[0, 1].grid(True, alpha=0.3)

    # 交易量对比
    axes[1, 0].scatter(enriched_klines['volume'], enriched_klines['total_volume'], alpha=0.6)
    axes[1, 0].plot([0, enriched_klines['volume'].max()], [0, enriched_klines['volume'].max()], 'r--', alpha=0.8)
    axes[1, 0].set_xlabel('K线成交量')
    axes[1, 0].set_ylabel('交易数据总成交量')
    axes[1, 0].set_title('K线成交量 vs 交易数据成交量')
    axes[1, 0].grid(True, alpha=0.3)

    # 交易数量与成交量关系
    axes[1, 1].scatter(enriched_klines['trade_count'], enriched_klines['volume'], alpha=0.6)
    axes[1, 1].set_xlabel('交易数量')
    axes[1, 1].set_ylabel('K线成交量')
    axes[1, 1].set_title('交易数量 vs K线成交量')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图表
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    chart_filename = f'{symbol.lower()}_analysis_{timestamp}.png'
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"图表已保存: {chart_filename}")

    plt.show()

    # 6. 总结
    print("\n=== 分析总结 ===")
    print(f"1. 数据时间范围: {trades_df['time'].min()} 到 {trades_df['time'].max()}")
    print(f"2. 平均每根K线的交易数量: {enriched_klines['trade_count'].mean():.2f}")
    print(f"3. K线成交量与交易数据成交量相关性: {correlation_data.loc['volume', 'total_volume']:.3f}")
    print(f"4. 交易数量与K线成交量相关性: {correlation_data.loc['trade_count', 'volume']:.3f}")

    print("\n分析完成! 建议使用Jupyter notebook进行更详细的分析。")

def analyze_trades_in_klines(trades_df, klines_df):
    """分析每个K线周期内的交易数据"""
    result_df = klines_df.copy()
    result_df['trade_count'] = 0
    result_df['total_volume'] = 0.0
    result_df['avg_price'] = 0.0
    result_df['price_std'] = 0.0

    for idx, kline in result_df.iterrows():
        start_time = kline['open_time']
        end_time = kline['close_time']

        mask = (trades_df['time'] >= start_time) & (trades_df['time'] <= end_time)
        period_trades = trades_df[mask]

        if len(period_trades) > 0:
            result_df.at[idx, 'trade_count'] = len(period_trades)
            result_df.at[idx, 'total_volume'] = period_trades['qty'].sum()
            result_df.at[idx, 'avg_price'] = period_trades['price'].mean()
            result_df.at[idx, 'price_std'] = period_trades['price'].std()

    return result_df

if __name__ == "__main__":
    main()
