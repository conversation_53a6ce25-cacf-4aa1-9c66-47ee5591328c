#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Binance现货交易对数据拉取脚本
拉取指定交易对的过去N个小时的trade和分钟K线数据
"""

import requests
import pandas as pd
import time
import json
from datetime import datetime, timedelta
import os
import argparse
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('binance_data_fetcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BinanceDataFetcher:
    """Binance数据拉取器"""

    def __init__(self):
        self.base_url = "https://api.binance.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def get_trades(self, symbol: str, limit: int = 1000) -> List[Dict]:
        """
        获取最近的交易数据

        Args:
            symbol: 交易对符号 (如 'BTCUSDT')
            limit: 获取的交易数量，最大1000

        Returns:
            交易数据列表
        """
        url = f"{self.base_url}/api/v3/trades"
        params = {
            'symbol': symbol.upper(),
            'limit': min(limit, 1000)
        }

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取trades数据失败: {e}")
            return []

    def get_klines(self, symbol: str, interval: str = '1m',
                   start_time: Optional[int] = None,
                   end_time: Optional[int] = None,
                   limit: int = 1000) -> List[List]:
        """
        获取K线数据

        Args:
            symbol: 交易对符号
            interval: K线间隔 ('1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M')
            start_time: 开始时间戳 (毫秒)
            end_time: 结束时间戳 (毫秒)
            limit: 获取的K线数量，最大1000

        Returns:
            K线数据列表
        """
        url = f"{self.base_url}/api/v3/klines"
        params = {
            'symbol': symbol.upper(),
            'interval': interval,
            'limit': min(limit, 1000)
        }

        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            return []

    def get_historical_trades(self, symbol: str, hours: int = 24) -> pd.DataFrame:
        """
        获取过去N个小时的历史交易数据

        Args:
            symbol: 交易对符号
            hours: 过去多少小时的数据

        Returns:
            交易数据DataFrame
        """
        logger.info(f"开始获取 {symbol} 过去 {hours} 小时的交易数据...")

        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        # 转换为毫秒时间戳
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(end_time.timestamp() * 1000)

        all_trades = []
        current_start = start_timestamp

        while current_start < end_timestamp:
            # 获取1000条交易记录
            trades = self.get_trades(symbol, limit=1000)
            if not trades:
                break

            # 过滤时间范围内的交易
            filtered_trades = []
            for trade in trades:
                trade_time = int(trade['time'])
                if start_timestamp <= trade_time <= end_timestamp:
                    filtered_trades.append(trade)
                elif trade_time < start_timestamp:
                    # 如果交易时间早于开始时间，说明已经获取完所有需要的数据
                    break

            all_trades.extend(filtered_trades)

            if len(trades) < 1000:
                break

            # 更新开始时间，获取下一批数据
            if trades:
                current_start = int(trades[-1]['time']) + 1

            # 添加延迟避免频率限制
            time.sleep(0.1)

        # 转换为DataFrame
        if all_trades:
            df = pd.DataFrame(all_trades)
            df['time'] = pd.to_datetime(df['time'], unit='ms')
            df['price'] = df['price'].astype(float)
            df['qty'] = df['qty'].astype(float)
            df['quoteQty'] = df['quoteQty'].astype(float)
            df = df.sort_values('time').reset_index(drop=True)

            logger.info(f"成功获取 {len(df)} 条交易记录")
            return df
        else:
            logger.warning("未获取到交易数据")
            return pd.DataFrame()

    def get_historical_klines(self, symbol: str, interval: str = '1m',
                             hours: int = 24) -> pd.DataFrame:
        """
        获取过去N个小时的K线数据

        Args:
            symbol: 交易对符号
            interval: K线间隔
            hours: 过去多少小时的数据

        Returns:
            K线数据DataFrame
        """
        logger.info(f"开始获取 {symbol} 过去 {hours} 小时的 {interval} K线数据...")

        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        # 转换为毫秒时间戳
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(end_time.timestamp() * 1000)

        all_klines = []
        current_start = start_timestamp

        while current_start < end_timestamp:
            # 计算当前批次的结束时间
            current_end = min(current_start + (1000 * self._get_interval_ms(interval)), end_timestamp)

            klines = self.get_klines(
                symbol=symbol,
                interval=interval,
                start_time=current_start,
                end_time=current_end,
                limit=1000
            )

            if not klines:
                break

            all_klines.extend(klines)

            if len(klines) < 1000:
                break

            # 更新开始时间
            current_start = int(klines[-1][0]) + 1

            # 添加延迟避免频率限制
            time.sleep(0.1)

        # 转换为DataFrame
        if all_klines:
            columns = ['open_time', 'open', 'high', 'low', 'close', 'volume',
                      'close_time', 'quote_asset_volume', 'number_of_trades',
                      'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore']

            df = pd.DataFrame(all_klines, columns=columns)

            # 转换数据类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume',
                             'quote_asset_volume', 'taker_buy_base_asset_volume',
                             'taker_buy_quote_asset_volume']

            for col in numeric_columns:
                df[col] = df[col].astype(float)

            df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
            df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
            df['number_of_trades'] = df['number_of_trades'].astype(int)

            df = df.sort_values('open_time').reset_index(drop=True)

            logger.info(f"成功获取 {len(df)} 条K线记录")
            return df
        else:
            logger.warning("未获取到K线数据")
            return pd.DataFrame()

    def _get_interval_ms(self, interval: str) -> int:
        """获取时间间隔对应的毫秒数"""
        interval_map = {
            '1m': 60 * 1000,
            '3m': 3 * 60 * 1000,
            '5m': 5 * 60 * 1000,
            '15m': 15 * 60 * 1000,
            '30m': 30 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '2h': 2 * 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '6h': 6 * 60 * 60 * 1000,
            '8h': 8 * 60 * 60 * 1000,
            '12h': 12 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000,
            '3d': 3 * 24 * 60 * 60 * 1000,
            '1w': 7 * 24 * 60 * 60 * 1000,
            '1M': 30 * 24 * 60 * 60 * 1000
        }
        return interval_map.get(interval, 60 * 1000)

    def save_data(self, df: pd.DataFrame, filename: str, data_type: str):
        """保存数据到文件"""
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)

        filepath = os.path.join('data', filename)
        df.to_csv(filepath, index=False)
        logger.info(f"{data_type}数据已保存到: {filepath}")

    def fetch_and_save_data(self, symbol: str, hours: int = 24,
                           kline_interval: str = '1m'):
        """
        获取并保存交易数据和K线数据

        Args:
            symbol: 交易对符号
            hours: 过去多少小时的数据
            kline_interval: K线间隔
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 获取交易数据
        trades_df = self.get_historical_trades(symbol, hours)
        if not trades_df.empty:
            trades_filename = f"{symbol.lower()}_trades_{timestamp}.csv"
            self.save_data(trades_df, trades_filename, "交易")

        # 获取K线数据
        klines_df = self.get_historical_klines(symbol, kline_interval, hours)
        if not klines_df.empty:
            klines_filename = f"{symbol.lower()}_{kline_interval}_klines_{timestamp}.csv"
            self.save_data(klines_df, klines_filename, "K线")

        return trades_df, klines_df

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Binance数据拉取工具')
    parser.add_argument('--symbol', type=str, default='BTCUSDT',
                       help='交易对符号 (默认: BTCUSDT)')
    parser.add_argument('--hours', type=int, default=24,
                       help='过去多少小时的数据 (默认: 24)')
    parser.add_argument('--interval', type=str, default='1m',
                       help='K线间隔 (默认: 1m)')

    args = parser.parse_args()

    fetcher = BinanceDataFetcher()
    trades_df, klines_df = fetcher.fetch_and_save_data(
        symbol=args.symbol,
        hours=args.hours,
        kline_interval=args.interval
    )

    print(f"\n数据获取完成!")
    print(f"交易数据: {len(trades_df)} 条记录")
    print(f"K线数据: {len(klines_df)} 条记录")

if __name__ == "__main__":
    main()
