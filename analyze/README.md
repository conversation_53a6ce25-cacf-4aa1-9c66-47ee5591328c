# Binance交易数据与K线关系分析

本项目包含用于拉取Binance现货交易对数据并分析交易数据与K线关系关系的工具。

## 文件说明

- `binance_data_fetcher.py`: Binance数据拉取脚本
- `trade_kline_analysis.ipynb`: Jupyter notebook分析文件
- `requirements.txt`: Python依赖包列表
- `data/`: 数据存储目录（自动创建）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 拉取数据

使用Python脚本拉取指定交易对的数据：

```bash
# 拉取BTCUSDT过去24小时的1分钟K线数据
python binance_data_fetcher.py --symbol BTCUSDT --hours 24 --interval 1m

# 拉取ETHUSDT过去48小时的5分钟K线数据
python binance_data_fetcher.py --symbol ETHUSDT --hours 48 --interval 5m

# 查看帮助
python binance_data_fetcher.py --help
```

参数说明：
- `--symbol`: 交易对符号（默认：BTCUSDT）
- `--hours`: 过去多少小时的数据（默认：24）
- `--interval`: K线间隔（默认：1m）

支持的K线间隔：
- 1m, 3m, 5m, 15m, 30m
- 1h, 2h, 4h, 6h, 8h, 12h
- 1d, 3d, 1w, 1M

### 2. 数据分析

启动Jupyter notebook进行分析：

```bash
jupyter notebook trade_kline_analysis.ipynb
```

或者在Jupyter Lab中打开：

```bash
jupyter lab
```

## 分析内容

notebook包含以下分析：

1. **数据概览**: 查看交易数据和K线数据的基本信息
2. **时间序列分析**: 可视化价格和交易量的时间变化
3. **关联分析**: 分析交易数据与K线数据的关系
4. **交易量分析**: 研究交易量与K线成交量的关系
5. **价格关系分析**: 比较交易价格与K线价格
6. **相关性分析**: 计算各变量间的相关性
7. **异常检测**: 识别异常的交易模式

## 数据文件格式

### 交易数据 (trades)
- `time`: 交易时间
- `price`: 交易价格
- `qty`: 交易数量
- `quoteQty`: 交易金额
- `isBuyerMaker`: 是否为买方挂单成交

### K线数据 (klines)
- `open_time`: 开盘时间
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `volume`: 成交量
- `close_time`: 收盘时间
- `quote_asset_volume`: 成交金额
- `number_of_trades`: 成交笔数

## 注意事项

1. **API限制**: Binance API有频率限制，脚本已添加延迟避免超限
2. **数据完整性**: 建议在网络稳定的环境下运行
3. **存储空间**: 大量数据会占用较多存储空间
4. **时间范围**: 建议单次拉取不超过7天的数据

## 示例输出

运行脚本后会在`data/`目录下生成以下文件：
- `btcusdt_trades_20241201_143022.csv`: 交易数据
- `btcusdt_1m_klines_20241201_143022.csv`: K线数据

## 故障排除

1. **网络连接问题**: 检查网络连接和防火墙设置
2. **API限制**: 如果遇到频率限制，增加脚本中的延迟时间
3. **数据为空**: 检查交易对符号是否正确
4. **依赖问题**: 确保所有依赖包都已正确安装

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多交易所
- 添加更多技术指标
- 实现实时数据监控
- 添加机器学习模型
- 支持更多数据格式导出
