# Bitget历史数据获取工具

这个工具用于获取Bitget交易所指定合约币种的orderbook和trades历史数据。

## 功能特性

- 🔍 **交易对验证**: 自动验证交易对是否存在
- 📊 **Orderbook数据**: 获取实时orderbook深度数据
- 💹 **交易数据**: 获取最近的交易记录
- 📈 **K线数据**: 获取历史K线数据
- 💾 **数据保存**: 支持保存为CSV格式
- ⏰ **时间范围**: 支持指定小时数获取历史数据

## 文件说明

- `bitget_historical_data_fetcher.py` - 主要的数据获取类
- `bitget_data_example.py` - 使用示例和演示
- `README_bitget.md` - 本说明文档

## 安装依赖

```bash
pip install requests pandas
```

## 快速开始

### 1. 命令行使用

```bash
# 获取BTCUSDT过去2小时的数据（默认）
python bitget_historical_data_fetcher.py BTCUSDT

# 获取ETHUSDT过去6小时的数据
python bitget_historical_data_fetcher.py ETHUSDT -n 6

# 指定输出目录
python bitget_historical_data_fetcher.py BTCUSDT -o ./data -n 4
```

### 2. 编程使用

```python
from bitget_historical_data_fetcher import BitgetHistoricalDataFetcher

# 创建客户端
client = BitgetHistoricalDataFetcher()

# 获取orderbook快照
orderbook = client.get_orderbook_snapshot("BTCUSDT", limit=100)

# 获取最近交易
trades = client.get_recent_trades("BTCUSDT", limit=100)

# 获取K线数据
klines = client.get_kline_data("BTCUSDT", granularity='1m', limit=60)

# 收集历史数据
data = client.collect_historical_data("BTCUSDT", hours=2)

# 保存数据
client.save_to_csv(data, output_dir="./output")
```

## 参数说明

### 命令行参数

- `symbol`: 交易对名称，如 BTCUSDT 或 BTCUSDT_UMCBL
- `-o, --output`: 输出目录（默认：当前目录）
- `-n, --hours`: 获取过去多少小时的数据（默认：2小时）
- `-i, --interval`: 数据收集间隔秒数（默认：60秒）

### API方法参数

#### get_orderbook_snapshot()
- `symbol`: 交易对名称
- `limit`: 深度档数（默认：100）

#### get_recent_trades()
- `symbol`: 交易对名称
- `limit`: 交易记录数量（默认：100）

#### get_kline_data()
- `symbol`: 交易对名称
- `granularity`: K线周期（1m, 5m, 15m, 30m, 1h, 4h, 1d等）
- `limit`: K线数量（默认：100）
- `start_time`: 开始时间戳（可选）
- `end_time`: 结束时间戳（可选）

## 支持的交易对格式

- `BTCUSDT` - 标准格式
- `BTCUSDT_UMCBL` - 带后缀格式
- 工具会自动处理格式转换

## 输出文件格式

### K线数据 (klines)
```csv
symbol,timestamp,datetime,open,high,low,close,volume,quote_volume
BTCUSDT,1703123400000,2023-12-21 10:30:00,42150.5,42200.0,42100.0,42180.5,125.45,5294567.25
```

### 交易数据 (trades)
```csv
symbol,tradeId,price,size,side,timestamp,datetime
BTCUSDT,123456789,42180.5,0.025,buy,1703123456789,2023-12-21 10:30:56
```

### Orderbook数据 (orderbook)
```csv
symbol,timestamp,datetime,side,level,price,size
BTCUSDT,1703123456789,2023-12-21 10:30:56,bid,1,42180.5,1.25
BTCUSDT,1703123456789,2023-12-21 10:30:56,ask,1,42181.0,0.85
```

## 使用示例

### 示例1：基础数据获取

```python
from bitget_historical_data_fetcher import BitgetHistoricalDataFetcher

client = BitgetHistoricalDataFetcher()

# 验证交易对
if client.validate_symbol("BTCUSDT"):
    print("交易对有效")
    
    # 获取orderbook
    orderbook = client.get_orderbook_snapshot("BTCUSDT", limit=10)
    print(f"买盘档数: {len(orderbook['bids'])}")
    print(f"卖盘档数: {len(orderbook['asks'])}")
```

### 示例2：批量获取多个交易对

```python
symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]

for symbol in symbols:
    if client.validate_symbol(symbol):
        data = client.collect_historical_data(symbol, hours=1)
        client.save_to_csv(data, f"./data/{symbol}")
```

### 示例3：数据分析

```python
# 获取K线数据进行分析
klines = client.get_kline_data("BTCUSDT", granularity='5m', limit=20)

if klines:
    prices = [kline['close'] for kline in klines]
    avg_price = sum(prices) / len(prices)
    price_volatility = (max(prices) - min(prices)) / avg_price * 100
    
    print(f"平均价格: {avg_price:.2f}")
    print(f"价格波动率: {price_volatility:.2f}%")
```

## 运行示例

```bash
# 运行完整示例
python bitget_data_example.py
```

这将运行所有示例，包括：
- 基础使用示例
- 历史数据收集示例
- 多交易对示例
- 数据分析示例

## 注意事项

1. **API限制**: Bitget API有请求频率限制，建议在请求间添加适当延时
2. **网络连接**: 确保网络连接稳定，工具会自动重试失败的请求
3. **数据时效性**: 交易数据和orderbook数据是实时的，K线数据有一定延迟
4. **存储空间**: 长时间收集数据会产生大量文件，注意磁盘空间
5. **交易对格式**: 确保使用正确的交易对格式，工具会自动验证

## 错误处理

工具包含完善的错误处理机制：
- 自动重试失败的请求
- 验证交易对有效性
- 处理网络连接问题
- 提供详细的错误信息

## 扩展功能

可以根据需要扩展以下功能：
- 添加更多数据类型（如资金费率、持仓数据等）
- 支持实时数据流
- 添加数据分析功能
- 支持更多输出格式（JSON、数据库等）

## 技术支持

如有问题或建议，请查看代码注释或联系开发团队。
