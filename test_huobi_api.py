#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Huobi API的正确端点
"""

import requests
import time

def test_huobi_endpoints():
    """测试不同的Huobi API端点"""
    
    # 测试symbol
    test_symbols = ["MANAUSDT", "BTCUSDT", "ETHUSDT"]
    
    # 可能的API端点
    base_url = "https://api.huobi.pro"
    endpoints = [
        "/swap-ex/market/detail/merged",  # 币本位永续
        "/linear-swap-ex/market/detail/merged",  # U本位永续
        "/v2/market/detail/merged",  # 备用端点
        "/market/detail/merged",  # 现货端点
        "/swap-api/v1/swap_detail",  # 另一个永续端点
        "/linear-swap-api/v1/swap_detail",  # 线性永续端点
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "application/json",
    }
    
    for symbol in test_symbols:
        print(f"\n测试币种: {symbol}")
        print("=" * 50)
        
        # 不同的symbol格式
        symbol_formats = [
            symbol.upper(),  # MANAUSDT
            symbol.lower(),  # manausdt
            f"{symbol[:-4]}-USDT",  # MANA-USDT
            f"{symbol[:-4]}-{symbol[-4:]}",  # MANA-USDT
        ]
        
        for endpoint in endpoints:
            print(f"\n尝试端点: {endpoint}")
            
            for symbol_format in symbol_formats:
                try:
                    url = f"{base_url}{endpoint}"
                    params = {"contract_code": symbol_format}
                    
                    response = requests.get(url, params=params, headers=headers, timeout=10)
                    
                    print(f"  Symbol: {symbol_format:15} Status: {response.status_code}", end="")
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get("status") == "ok" and data.get("tick"):
                            amount = data["tick"].get("amount", 0)
                            print(f" ✓ 成功! 24h成交额: {amount}")
                            break
                        else:
                            print(f" ✗ 响应格式错误: {data.get('err-msg', 'Unknown')}")
                    elif response.status_code == 404:
                        print(" ✗ 404 Not Found")
                    else:
                        print(f" ✗ HTTP {response.status_code}")
                        
                except Exception as e:
                    print(f" ✗ 异常: {e}")
                    
                time.sleep(0.1)  # 避免请求过于频繁
            
            time.sleep(0.5)  # 端点间稍作停顿

def test_huobi_public_api():
    """测试Huobi公开API"""
    print("\n测试Huobi公开API...")
    
    try:
        # 尝试获取所有永续合约信息
        url = "https://api.huobi.pro/linear-swap-api/v1/swap_contract_info"
        response = requests.get(url, timeout=10)
        
        print(f"合约信息API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "ok" and data.get("data"):
                contracts = data["data"]
                print(f"找到 {len(contracts)} 个永续合约")
                
                # 显示前几个合约
                for i, contract in enumerate(contracts[:5]):
                    symbol = contract.get("contract_code", "Unknown")
                    print(f"  {i+1}. {symbol}")
                    
                # 查找MANA相关合约
                mana_contracts = [c for c in contracts if "MANA" in c.get("contract_code", "")]
                if mana_contracts:
                    print(f"\nMANA相关合约:")
                    for contract in mana_contracts:
                        print(f"  - {contract.get('contract_code')}")
                else:
                    print("\n未找到MANA相关合约")
            else:
                print(f"API响应错误: {data}")
        else:
            print(f"API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"测试公开API失败: {e}")

if __name__ == "__main__":
    print("开始测试Huobi API端点...")
    test_huobi_public_api()
    test_huobi_endpoints()
