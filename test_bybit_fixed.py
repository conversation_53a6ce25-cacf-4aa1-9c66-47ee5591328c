#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Bybit API方法
"""

import sys
import os

# 添加analyze目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'analyze'))

def test_bybit_method():
    """测试修复后的Bybit方法"""
    try:
        from combined_analysis import CombinedAnalyzer
        
        analyzer = CombinedAnalyzer()
        
        # 测试几个币种
        test_symbols = ["BTCUSDT", "ETHUSDT", "MANAUSDT"]
        
        print("测试修复后的Bybit API方法...")
        print("=" * 50)
        
        for symbol in test_symbols:
            print(f"\n测试 {symbol}:")
            try:
                volume = analyzer.get_bybit_24hr_ticker(symbol)
                if volume > 0:
                    print(f"  ✓ 成功获取24小时交易量: {analyzer.format_volume(volume)}")
                else:
                    print(f"  ✗ 获取失败或交易量为0")
            except Exception as e:
                print(f"  ✗ 异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_all_exchanges_updated():
    """测试所有交易所的24小时数据获取（包括修复后的Bybit）"""
    try:
        from combined_analysis import CombinedAnalyzer
        
        analyzer = CombinedAnalyzer()
        
        test_symbol = "BTCUSDT"
        
        print(f"\n\n测试所有交易所获取 {test_symbol} 24小时数据（包括修复后的Bybit）...")
        print("=" * 70)
        
        # 测试所有交易所
        result = analyzer.get_all_exchanges_24hr_volume(test_symbol)
        
        print(f"\n各交易所数据:")
        for exchange, volume in result.items():
            if volume > 0:
                print(f"  {exchange}: {analyzer.format_volume(volume)}")
            else:
                print(f"  {exchange}: N/A")
        
        total_volume = sum(result.values())
        print(f"\n总计其他交易所期货交易量: {analyzer.format_volume(total_volume)}")
        
        # 检查Bybit是否成功
        bybit_volume = result.get("Bybit", 0)
        if bybit_volume > 0:
            print("✓ Bybit API修复成功！")
        else:
            print("⚠ Bybit数据仍然获取失败")
        
        if total_volume > 0:
            print("✓ 成功获取其他交易所数据")
            return True
        else:
            print("✗ 所有交易所数据获取失败")
            return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试修复后的Bybit API...")
    
    success1 = test_bybit_method()
    success2 = test_all_exchanges_updated()
    
    if success1 and success2:
        print("\n" + "=" * 70)
        print("✓ 所有测试通过！Bybit API修复成功")
        print("\n现在可以正常获取:")
        print("- Huobi 24小时期货交易量")
        print("- Bybit 24小时期货交易量") 
        print("- 其他交易所期货交易量")
        print("- 现货与非Binance期货交易量比值计算")
    else:
        print("\n" + "=" * 70)
        print("✗ 部分测试失败，需要进一步调试")
